-- 缓考申请和审核菜单权限配置
-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考管理', '2000', '5', 'defer', NULL, 1, 0, 'M', '0', '0', NULL, 'fa fa-clock-o', 'admin', sysdate(), '', NULL, '缓考申请和审核管理');

-- 获取刚插入的缓考管理菜单ID
SET @parentId = LAST_INSERT_ID();

-- 缓考申请菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请', @parentId, '1', 'defer', 'system/defer/defer', 1, 0, 'C', '0', '0', 'system:defer:view', 'fa fa-edit', 'admin', sysdate(), '', NULL, '学生缓考申请');

-- 获取缓考申请菜单ID
SET @deferMenuId = LAST_INSERT_ID();

-- 缓考申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请查询', @deferMenuId, '1', '', '', 1, 0, 'F', '0', '0', 'system:defer:list', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请新增', @deferMenuId, '2', '', '', 1, 0, 'F', '0', '0', 'system:defer:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请修改', @deferMenuId, '3', '', '', 1, 0, 'F', '0', '0', 'system:defer:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请删除', @deferMenuId, '4', '', '', 1, 0, 'F', '0', '0', 'system:defer:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请导出', @deferMenuId, '5', '', '', 1, 0, 'F', '0', '0', 'system:defer:export', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考申请详情', @deferMenuId, '6', '', '', 1, 0, 'F', '0', '0', 'system:defer:detail', '#', 'admin', sysdate(), '', NULL, '');

-- 缓考审核菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考审核', @parentId, '2', 'deferAudit', 'system/deferAudit/deferAudit', 1, 0, 'C', '0', '0', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', sysdate(), '', NULL, '教师缓考审核');

-- 获取缓考审核菜单ID
SET @deferAuditMenuId = LAST_INSERT_ID();

-- 缓考审核按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考审核查询', @deferAuditMenuId, '1', '', '', 1, 0, 'F', '0', '0', 'system:deferAudit:list', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考审核操作', @deferAuditMenuId, '2', '', '', 1, 0, 'F', '0', '0', 'system:deferAudit:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考审核导出', @deferAuditMenuId, '3', '', '', 1, 0, 'F', '0', '0', 'system:deferAudit:export', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('缓考审核详情', @deferAuditMenuId, '4', '', '', 1, 0, 'F', '0', '0', 'system:deferAudit:detail', '#', 'admin', sysdate(), '', NULL, '');

-- 角色权限分配示例（可根据实际需要调整）
-- 学生角色权限（假设学生角色ID为3）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 3, menu_id FROM sys_menu WHERE perms IN (
    'system:defer:view',
    'system:defer:list', 
    'system:defer:add', 
    'system:defer:edit', 
    'system:defer:remove',
    'system:defer:detail'
);

-- 教师角色权限（假设教师角色ID为4）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 4, menu_id FROM sys_menu WHERE perms IN (
    'system:deferAudit:view',
    'system:deferAudit:list',
    'system:deferAudit:edit',
    'system:deferAudit:export',
    'system:deferAudit:detail'
);

-- 管理员角色自动拥有所有权限（role_id=1通常是超级管理员）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%缓考%';

-- 创建学生和教师角色（如果不存在）
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES ('学生', 'student', 3, '5', 1, 1, '0', '0', 'admin', sysdate(), '学生角色')
ON DUPLICATE KEY UPDATE role_name = role_name;

INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES ('教师', 'teacher', 4, '2', 1, 1, '0', '0', 'admin', sysdate(), '教师角色')
ON DUPLICATE KEY UPDATE role_name = role_name;
