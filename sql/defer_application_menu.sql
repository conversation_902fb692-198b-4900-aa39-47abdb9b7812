-- 缓考申请和审核菜单权限配置
-- 使用数据库名
USE ry_vue;

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-clock-o', 'admin', now(), '缓考申请和审核管理');

-- 获取刚插入的缓考管理菜单ID
SET @parentId = LAST_INSERT_ID();

-- 缓考申请菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请', @parentId, '1', '/system/defer', '', 'C', '0', '1', 'system:defer:view', 'fa fa-edit', 'admin', now(), '学生缓考申请');

-- 获取缓考申请菜单ID
SET @deferMenuId = LAST_INSERT_ID();

-- 缓考申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请查询', @deferMenuId, '1', '#', '', 'F', '0', '1', 'system:defer:list', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请新增', @deferMenuId, '2', '#', '', 'F', '0', '1', 'system:defer:add', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请修改', @deferMenuId, '3', '#', '', 'F', '0', '1', 'system:defer:edit', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请删除', @deferMenuId, '4', '#', '', 'F', '0', '1', 'system:defer:remove', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请导出', @deferMenuId, '5', '#', '', 'F', '0', '1', 'system:defer:export', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请详情', @deferMenuId, '6', '#', '', 'F', '0', '1', 'system:defer:detail', '#', 'admin', now(), '');

-- 缓考审核菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核', @parentId, '2', '/system/deferAudit', '', 'C', '0', '1', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', now(), '教师缓考审核');

-- 获取缓考审核菜单ID
SET @deferAuditMenuId = LAST_INSERT_ID();

-- 缓考审核按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核查询', @deferAuditMenuId, '1', '#', '', 'F', '0', '1', 'system:deferAudit:list', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核操作', @deferAuditMenuId, '2', '#', '', 'F', '0', '1', 'system:deferAudit:edit', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核导出', @deferAuditMenuId, '3', '#', '', 'F', '0', '1', 'system:deferAudit:export', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核详情', @deferAuditMenuId, '4', '#', '', 'F', '0', '1', 'system:deferAudit:detail', '#', 'admin', now(), '');

-- 管理员角色自动拥有所有权限（role_id=1通常是超级管理员）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%缓考%';
