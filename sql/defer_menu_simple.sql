-- 缓考申请和审核菜单权限配置（简化版）
-- 请在数据库管理工具中逐条执行

-- 1. 创建缓考管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-clock-o', 'admin', now(), '缓考申请和审核管理');

-- 2. 创建缓考申请菜单（请将下面的 PARENT_ID 替换为上面插入记录的实际ID）
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请', 'PARENT_ID', '1', '/system/defer', '', 'C', '0', '1', 'system:defer:view', 'fa fa-edit', 'admin', now(), '学生缓考申请');

-- 3. 创建缓考申请按钮权限（请将下面的 DEFER_MENU_ID 替换为上面插入记录的实际ID）
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请查询', 'DEFER_MENU_ID', '1', '#', '', 'F', '0', '1', 'system:defer:list', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请新增', 'DEFER_MENU_ID', '2', '#', '', 'F', '0', '1', 'system:defer:add', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请修改', 'DEFER_MENU_ID', '3', '#', '', 'F', '0', '1', 'system:defer:edit', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请删除', 'DEFER_MENU_ID', '4', '#', '', 'F', '0', '1', 'system:defer:remove', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请导出', 'DEFER_MENU_ID', '5', '#', '', 'F', '0', '1', 'system:defer:export', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考申请详情', 'DEFER_MENU_ID', '6', '#', '', 'F', '0', '1', 'system:defer:detail', '#', 'admin', now(), '');

-- 4. 创建缓考审核菜单（请将下面的 PARENT_ID 替换为第1步插入记录的实际ID）
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核', 'PARENT_ID', '2', '/system/deferAudit', '', 'C', '0', '1', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', now(), '教师缓考审核');

-- 5. 创建缓考审核按钮权限（请将下面的 DEFER_AUDIT_MENU_ID 替换为上面插入记录的实际ID）
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核查询', 'DEFER_AUDIT_MENU_ID', '1', '#', '', 'F', '0', '1', 'system:deferAudit:list', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核操作', 'DEFER_AUDIT_MENU_ID', '2', '#', '', 'F', '0', '1', 'system:deferAudit:edit', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核导出', 'DEFER_AUDIT_MENU_ID', '3', '#', '', 'F', '0', '1', 'system:deferAudit:export', '#', 'admin', now(), '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考审核详情', 'DEFER_AUDIT_MENU_ID', '4', '#', '', 'F', '0', '1', 'system:deferAudit:detail', '#', 'admin', now(), '');

-- 6. 为管理员角色分配权限（可选）
-- INSERT INTO sys_role_menu (role_id, menu_id) SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%缓考%';
