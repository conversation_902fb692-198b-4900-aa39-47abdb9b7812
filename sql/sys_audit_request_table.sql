-- 审核申请表
DROP TABLE IF EXISTS sys_audit_request;
CREATE TABLE sys_audit_request (
  request_id        bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '申请ID',
  student_id        bigint(20)      NOT NULL                   COMMENT '学生ID',
  student_name      varchar(100)    NOT NULL                   COMMENT '学生姓名',
  student_no        varchar(50)     NOT NULL                   COMMENT '学号',
  request_type      varchar(20)     NOT NULL                   COMMENT '申请类型(defer:缓考,exempt:免考,makeup:补考,retest:重测)',
  subject_name      varchar(100)    DEFAULT NULL               COMMENT '申请科目名称',
  request_reason    varchar(20)     NOT NULL                   COMMENT '申请原因(illness:疾病,injury:受伤,emergency:紧急事务,other:其他)',
  description       text            DEFAULT NULL               COMMENT '详细说明描述',
  attachment_urls   text            DEFAULT NULL               COMMENT '附件URL列表(多个URL用逗号分隔)',
  request_status    varchar(20)     DEFAULT 'pending'          COMMENT '申请状态(pending:待审核,approved:审核通过,rejected:审核驳回)',
  submit_time       datetime        DEFAULT CURRENT_TIMESTAMP  COMMENT '提交时间',
  audit_user_name   varchar(100)    DEFAULT NULL               COMMENT '审核人姓名',
  audit_time        datetime        DEFAULT NULL               COMMENT '审核时间',
  audit_comment     text            DEFAULT NULL               COMMENT '审核意见/驳回理由',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (request_id),
  KEY idx_student_id (student_id),
  KEY idx_student_no (student_no),
  KEY idx_request_type (request_type),
  KEY idx_request_status (request_status),
  KEY idx_submit_time (submit_time),
  KEY idx_audit_time (audit_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '审核申请表';

-- 插入示例数据
INSERT INTO sys_audit_request (student_id, student_name, student_no, request_type, subject_name, request_reason, description, request_status, submit_time, create_by, create_time, remark) VALUES
(1, '张三', '2021001001', 'defer', '50米跑', 'illness', '因感冒发烧，医生建议休息一周，无法参加本次体测', 'pending', '2025-01-15 09:30:00', 'student', NOW(), '已提供医院诊断证明'),
(2, '李四', '2021001002', 'exempt', '引体向上', 'injury', '右肩关节脱臼，正在康复治疗中，暂时无法进行上肢力量测试', 'approved', '2025-01-14 14:20:00', 'student', NOW(), '康复期预计3个月'),
(3, '王五', '2021001003', 'makeup', '1000米跑', 'emergency', '家中有急事需要请假回家处理，错过了原定的测试时间', 'rejected', '2025-01-13 16:45:00', 'student', NOW(), '申请补测'),
(4, '赵六', '2021001004', 'defer', '仰卧起坐', 'illness', '急性肠胃炎，身体不适，无法进行体能测试', 'pending', '2025-01-16 08:15:00', 'student', NOW(), '医院开具的病假条'),
(5, '钱七', '2021001005', 'exempt', '坐位体前屈', 'injury', '腰椎间盘突出，医生建议避免弯腰动作', 'approved', '2025-01-12 11:30:00', 'student', NOW(), '长期免测项目');

-- 更新审核信息
UPDATE sys_audit_request SET 
    audit_user_name = '体育老师A', 
    audit_time = '2025-01-15 10:30:00', 
    audit_comment = '同意缓考申请，请在身体恢复后及时补测',
    update_by = 'teacher',
    update_time = NOW()
WHERE request_id = 2 AND request_status = 'approved';

UPDATE sys_audit_request SET 
    audit_user_name = '体育老师B', 
    audit_time = '2025-01-14 09:15:00', 
    audit_comment = '家庭紧急事务不属于体测免考范围，建议申请缓考',
    update_by = 'teacher',
    update_time = NOW()
WHERE request_id = 3 AND request_status = 'rejected';

UPDATE sys_audit_request SET 
    audit_user_name = '体育老师A', 
    audit_time = '2025-01-13 15:20:00', 
    audit_comment = '根据医生诊断，同意该项目长期免测',
    update_by = 'teacher',
    update_time = NOW()
WHERE request_id = 5 AND request_status = 'approved';
