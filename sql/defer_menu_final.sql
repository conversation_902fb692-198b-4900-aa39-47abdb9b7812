-- 缓考申请和审核菜单权限配置（完全自动化版本）
-- 直接执行即可，无需手动替换ID

-- 1. 创建缓考管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-clock-o', 'admin', now(), '缓考申请和审核管理');

-- 2. 创建缓考申请菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请', menu_id, '1', '/system/defer', '', 'C', '0', '1', 'system:defer:view', 'fa fa-edit', 'admin', now(), '学生缓考申请'
FROM sys_menu WHERE menu_name = '缓考管理' AND parent_id = '0';

-- 3. 创建缓考申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请查询', menu_id, '1', '#', '', 'F', '0', '1', 'system:defer:list', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请新增', menu_id, '2', '#', '', 'F', '0', '1', 'system:defer:add', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请修改', menu_id, '3', '#', '', 'F', '0', '1', 'system:defer:edit', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请删除', menu_id, '4', '#', '', 'F', '0', '1', 'system:defer:remove', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请导出', menu_id, '5', '#', '', 'F', '0', '1', 'system:defer:export', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考申请详情', menu_id, '6', '#', '', 'F', '0', '1', 'system:defer:detail', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考申请' AND perms = 'system:defer:view';

-- 4. 创建缓考审核菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考审核', menu_id, '2', '/system/deferAudit', '', 'C', '0', '1', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', now(), '教师缓考审核'
FROM sys_menu WHERE menu_name = '缓考管理' AND parent_id = '0';

-- 5. 创建缓考审核按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考审核查询', menu_id, '1', '#', '', 'F', '0', '1', 'system:deferAudit:list', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考审核' AND perms = 'system:deferAudit:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考审核操作', menu_id, '2', '#', '', 'F', '0', '1', 'system:deferAudit:edit', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考审核' AND perms = 'system:deferAudit:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考审核导出', menu_id, '3', '#', '', 'F', '0', '1', 'system:deferAudit:export', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考审核' AND perms = 'system:deferAudit:view';

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
SELECT '缓考审核详情', menu_id, '4', '#', '', 'F', '0', '1', 'system:deferAudit:detail', '#', 'admin', now(), ''
FROM sys_menu WHERE menu_name = '缓考审核' AND perms = 'system:deferAudit:view';

-- 6. 为管理员角色分配所有缓考相关权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%缓考%' AND menu_id NOT IN (SELECT menu_id FROM sys_role_menu WHERE role_id = 1);

-- 查看插入结果
SELECT menu_id, menu_name, parent_id, perms, menu_type FROM sys_menu WHERE menu_name LIKE '%缓考%' ORDER BY menu_id;
