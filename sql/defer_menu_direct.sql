-- 缓考菜单直接插入版本（使用较大的ID避免冲突）

-- 1. 创建缓考管理主菜单 (使用ID 3000)
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES(3000, '缓考管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-clock-o', 'admin', now(), '缓考申请和审核管理');

-- 2. 创建缓考申请菜单 (使用ID 3001)
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES(3001, '缓考申请', '3000', '1', '/system/defer', '', 'C', '0', '1', 'system:defer:view', 'fa fa-edit', 'admin', now(), '学生缓考申请');

-- 3. 创建缓考申请按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(3002, '缓考申请查询', '3001', '1', '#', '', 'F', '0', '1', 'system:defer:list', '#', 'admin', now(), ''),
(3003, '缓考申请新增', '3001', '2', '#', '', 'F', '0', '1', 'system:defer:add', '#', 'admin', now(), ''),
(3004, '缓考申请修改', '3001', '3', '#', '', 'F', '0', '1', 'system:defer:edit', '#', 'admin', now(), ''),
(3005, '缓考申请删除', '3001', '4', '#', '', 'F', '0', '1', 'system:defer:remove', '#', 'admin', now(), ''),
(3006, '缓考申请导出', '3001', '5', '#', '', 'F', '0', '1', 'system:defer:export', '#', 'admin', now(), ''),
(3007, '缓考申请详情', '3001', '6', '#', '', 'F', '0', '1', 'system:defer:detail', '#', 'admin', now(), '');

-- 4. 创建缓考审核菜单 (使用ID 3010)
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES(3010, '缓考审核', '3000', '2', '/system/deferAudit', '', 'C', '0', '1', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', now(), '教师缓考审核');

-- 5. 创建缓考审核按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(3011, '缓考审核查询', '3010', '1', '#', '', 'F', '0', '1', 'system:deferAudit:list', '#', 'admin', now(), ''),
(3012, '缓考审核操作', '3010', '2', '#', '', 'F', '0', '1', 'system:deferAudit:edit', '#', 'admin', now(), ''),
(3013, '缓考审核导出', '3010', '3', '#', '', 'F', '0', '1', 'system:deferAudit:export', '#', 'admin', now(), ''),
(3014, '缓考审核详情', '3010', '4', '#', '', 'F', '0', '1', 'system:deferAudit:detail', '#', 'admin', now(), '');

-- 6. 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 3000), (1, 3001), (1, 3002), (1, 3003), (1, 3004), (1, 3005), (1, 3006), (1, 3007),
(1, 3010), (1, 3011), (1, 3012), (1, 3013), (1, 3014);

-- 7. 验证插入结果
SELECT menu_id, menu_name, parent_id, perms, menu_type FROM sys_menu WHERE menu_id BETWEEN 3000 AND 3014 ORDER BY menu_id;
