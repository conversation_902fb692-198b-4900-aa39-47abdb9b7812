-- 缓考菜单分步执行版本
-- 请按顺序逐条执行以下SQL语句

-- 第1步：创建缓考管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
VALUES('缓考管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-clock-o', 'admin', now(), '缓考申请和审核管理');

-- 第2步：查询刚插入的主菜单ID（记住这个ID，下面会用到）
SELECT menu_id, menu_name FROM sys_menu WHERE menu_name = '缓考管理' ORDER BY menu_id DESC LIMIT 1;

-- 第3步：创建缓考申请菜单（将下面的 XXX 替换为第2步查询到的menu_id）
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
-- VALUES('缓考申请', 'XXX', '1', '/system/defer', '', 'C', '0', '1', 'system:defer:view', 'fa fa-edit', 'admin', now(), '学生缓考申请');

-- 第4步：查询缓考申请菜单ID（记住这个ID）
-- SELECT menu_id, menu_name FROM sys_menu WHERE menu_name = '缓考申请' ORDER BY menu_id DESC LIMIT 1;

-- 第5步：创建缓考申请按钮权限（将下面的 YYY 替换为第4步查询到的menu_id）
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
-- ('缓考申请查询', 'YYY', '1', '#', '', 'F', '0', '1', 'system:defer:list', '#', 'admin', now(), ''),
-- ('缓考申请新增', 'YYY', '2', '#', '', 'F', '0', '1', 'system:defer:add', '#', 'admin', now(), ''),
-- ('缓考申请修改', 'YYY', '3', '#', '', 'F', '0', '1', 'system:defer:edit', '#', 'admin', now(), ''),
-- ('缓考申请删除', 'YYY', '4', '#', '', 'F', '0', '1', 'system:defer:remove', '#', 'admin', now(), ''),
-- ('缓考申请导出', 'YYY', '5', '#', '', 'F', '0', '1', 'system:defer:export', '#', 'admin', now(), ''),
-- ('缓考申请详情', 'YYY', '6', '#', '', 'F', '0', '1', 'system:defer:detail', '#', 'admin', now(), '');

-- 第6步：创建缓考审核菜单（将下面的 XXX 替换为第2步查询到的主菜单ID）
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark)
-- VALUES('缓考审核', 'XXX', '2', '/system/deferAudit', '', 'C', '0', '1', 'system:deferAudit:view', 'fa fa-check-square-o', 'admin', now(), '教师缓考审核');

-- 第7步：查询缓考审核菜单ID
-- SELECT menu_id, menu_name FROM sys_menu WHERE menu_name = '缓考审核' ORDER BY menu_id DESC LIMIT 1;

-- 第8步：创建缓考审核按钮权限（将下面的 ZZZ 替换为第7步查询到的menu_id）
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
-- ('缓考审核查询', 'ZZZ', '1', '#', '', 'F', '0', '1', 'system:deferAudit:list', '#', 'admin', now(), ''),
-- ('缓考审核操作', 'ZZZ', '2', '#', '', 'F', '0', '1', 'system:deferAudit:edit', '#', 'admin', now(), ''),
-- ('缓考审核导出', 'ZZZ', '3', '#', '', 'F', '0', '1', 'system:deferAudit:export', '#', 'admin', now(), ''),
-- ('缓考审核详情', 'ZZZ', '4', '#', '', 'F', '0', '1', 'system:deferAudit:detail', '#', 'admin', now(), '');

-- 第9步：为管理员分配权限
-- INSERT INTO sys_role_menu (role_id, menu_id) SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%缓考%';

-- 第10步：验证结果
-- SELECT menu_id, menu_name, parent_id, perms, menu_type FROM sys_menu WHERE menu_name LIKE '%缓考%' ORDER BY menu_id;
