-- 修复学生预约菜单
-- 先删除可能存在的重复菜单
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE url = '/system/studentReservation' OR menu_name = '学生预约'
);

DELETE FROM sys_menu WHERE url = '/system/studentReservation' OR menu_name = '学生预约';

-- 重新创建菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(6000, '学生预约', 0, 7, '/system/studentReservation', '', 'C', '0', '1', 'system:studentReservation:view', 'fa fa-calendar', 'admin', sysdate(), 'admin', sysdate(), '学生预约管理');

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, 6000);

-- 验证菜单创建
SELECT menu_id, menu_name, url, perms, visible FROM sys_menu WHERE menu_id = 6000;

-- 验证角色权限
SELECT rm.role_id, rm.menu_id, m.menu_name, m.url 
FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE rm.role_id = 1 AND rm.menu_id = 6000;
