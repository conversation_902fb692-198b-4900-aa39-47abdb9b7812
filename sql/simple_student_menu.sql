-- 简化的学生预约菜单SQL
-- 删除可能存在的旧菜单
DELETE FROM sys_role_menu WHERE menu_id IN (5000, 5001, 5002, 5003, 5004, 5005);
DELETE FROM sys_menu WHERE menu_id IN (5000, 5001, 5002, 5003, 5004, 5005);

-- 创建学生预约主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(5000, '学生预约', '0', '6', '/system/studentReservation', '', 'C', '0', '1', 'system:studentReservation:view', 'fa fa-calendar', 'admin', now(), '学生预约管理');

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, 5000);

-- 验证菜单是否创建成功
SELECT menu_id, menu_name, url, perms FROM sys_menu WHERE menu_id = 5000;
