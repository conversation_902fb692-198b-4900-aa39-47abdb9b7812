-- 修复 sys_student_score 表的 item_id 字段问题
-- 方案1：为 item_id 字段添加默认值

-- 如果表不存在，创建表
CREATE TABLE IF NOT EXISTS sys_student_score (
  score_id          bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '成绩ID',
  student_id        bigint(20)      NOT NULL                   COMMENT '学生ID',
  item_id           bigint(20)      DEFAULT NULL               COMMENT '考核项目ID',
  criterion_id      bigint(20)      DEFAULT NULL               COMMENT '使用的标准ID',
  raw_score         decimal(10,2)   DEFAULT NULL               COMMENT '原始成绩',
  grade_score       varchar(20)     DEFAULT ''                 COMMENT '等级成绩(excellent,good,pass,fail)',
  score_points      decimal(5,2)    DEFAULT NULL               COMMENT '分数(百分制)',
  exam_date         date            DEFAULT NULL               COMMENT '考试日期',
  exam_semester     varchar(20)     DEFAULT ''                 COMMENT '考试学期',
  exam_year         bigint(20)      DEFAULT NULL               COMMENT '考试年份',
  import_batch      varchar(50)     DEFAULT ''                 COMMENT '导入批次号',
  is_manual         char(1)         DEFAULT '0'                COMMENT '是否人工录入(0自动导入1人工录入)',
  status            char(1)         DEFAULT '1'                COMMENT '状态(0无效1有效)',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (score_id),
  KEY idx_student_id (student_id),
  KEY idx_item_id (item_id),
  KEY idx_exam_date (exam_date),
  KEY idx_exam_semester (exam_semester),
  KEY idx_status (status)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '学生成绩表';

-- 如果表已存在，修改 item_id 字段允许为 NULL
ALTER TABLE sys_student_score MODIFY COLUMN item_id bigint(20) DEFAULT NULL COMMENT '考核项目ID';

-- 创建考核项目表（如果不存在）
CREATE TABLE IF NOT EXISTS sys_test_item (
  item_id           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '项目ID',
  item_code         varchar(50)     NOT NULL                   COMMENT '项目编码',
  item_name         varchar(100)    NOT NULL                   COMMENT '项目名称',
  item_type         varchar(20)     DEFAULT 'PHYSICAL'         COMMENT '项目类型(PHYSICAL体能,SKILL技能)',
  unit              varchar(20)     DEFAULT ''                 COMMENT '计量单位',
  description       varchar(500)    DEFAULT ''                 COMMENT '项目描述',
  test_method       varchar(1000)   DEFAULT ''                 COMMENT '测试方法',
  scoring_method    varchar(1000)   DEFAULT ''                 COMMENT '评分方法',
  is_required       char(1)         DEFAULT '1'                COMMENT '是否必测(0否1是)',
  gender_limit      char(1)         DEFAULT '2'                COMMENT '性别限制(0男1女2不限)',
  min_age           int(3)          DEFAULT 0                  COMMENT '最小年龄',
  max_age           int(3)          DEFAULT 100                COMMENT '最大年龄',
  sort_order        int(4)          DEFAULT 0                  COMMENT '排序',
  status            char(1)         DEFAULT '1'                COMMENT '状态(0停用1正常)',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (item_id),
  UNIQUE KEY uk_item_code (item_code),
  KEY idx_item_type (item_type),
  KEY idx_status (status)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '考核项目表';

-- 插入默认的考核项目数据
INSERT INTO sys_test_item (item_code, item_name, item_type, unit, description, is_required, gender_limit, sort_order, status, create_by, create_time) VALUES
('HEIGHT_WEIGHT', '身高体重', 'PHYSICAL', 'cm/kg', '测量学生身高和体重，计算BMI指数', '1', '2', 1, '1', 'admin', NOW()),
('VITAL_CAPACITY', '肺活量', 'PHYSICAL', 'ml', '测量学生肺活量，评估心肺功能', '1', '2', 2, '1', 'admin', NOW()),
('RUN_50M', '50米跑', 'PHYSICAL', '秒', '测量学生50米跑步时间，评估速度素质', '1', '2', 3, '1', 'admin', NOW()),
('STANDING_JUMP', '立定跳远', 'PHYSICAL', 'cm', '测量学生立定跳远距离，评估爆发力', '1', '2', 4, '1', 'admin', NOW()),
('SIT_REACH', '坐位体前屈', 'PHYSICAL', 'cm', '测量学生坐位体前屈距离，评估柔韧性', '1', '2', 5, '1', 'admin', NOW()),
('SIT_UP', '仰卧起坐', 'PHYSICAL', '个/分钟', '测量女学生1分钟仰卧起坐个数，评估腹肌力量', '1', '1', 6, '1', 'admin', NOW()),
('PULL_UP', '引体向上', 'PHYSICAL', '个', '测量男学生引体向上个数，评估上肢力量', '1', '0', 7, '1', 'admin', NOW()),
('RUN_800M', '800米跑', 'PHYSICAL', '分:秒', '测量女学生800米跑步时间，评估耐力素质', '1', '1', 8, '1', 'admin', NOW()),
('RUN_1000M', '1000米跑', 'PHYSICAL', '分:秒', '测量男学生1000米跑步时间，评估耐力素质', '1', '0', 9, '1', 'admin', NOW())
ON DUPLICATE KEY UPDATE 
item_name = VALUES(item_name),
description = VALUES(description),
update_time = NOW();
