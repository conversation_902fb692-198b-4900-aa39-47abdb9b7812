-- 成绩标准表
DROP TABLE IF EXISTS sys_score_criterion;
CREATE TABLE sys_score_criterion (
  criterion_id      bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '标准ID',
  item_id           bigint(20)      NOT NULL                   COMMENT '考核项目ID',
  gender            char(1)         NOT NULL                   COMMENT '性别(M男F女)',
  grade_level       varchar(20)     NOT NULL                   COMMENT '年级(freshman,sophomore,junior,senior)',
  excellent_min     decimal(10,2)   DEFAULT NULL               COMMENT '优秀最低值',
  excellent_max     decimal(10,2)   DEFAULT NULL               COMMENT '优秀最高值',
  good_min          decimal(10,2)   DEFAULT NULL               COMMENT '良好最低值',
  good_max          decimal(10,2)   DEFAULT NULL               COMMENT '良好最高值',
  pass_min          decimal(10,2)   DEFAULT NULL               COMMENT '及格最低值',
  pass_max          decimal(10,2)   DEFAULT NULL               COMMENT '及格最高值',
  fail_min          decimal(10,2)   DEFAULT NULL               COMMENT '不及格最低值',
  fail_max          decimal(10,2)   DEFAULT NULL               COMMENT '不及格最高值',
  is_reverse        char(1)         DEFAULT '0'                COMMENT '是否反向计算(0正向1反向,如跑步时间越少越好)',
  effective_date    date            NOT NULL                   COMMENT '生效日期',
  expire_date       date            DEFAULT NULL               COMMENT '失效日期',
  version           varchar(20)     DEFAULT '1.0'              COMMENT '版本号',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (criterion_id),
  KEY idx_item_id (item_id),
  KEY idx_gender (gender),
  KEY idx_grade_level (grade_level),
  KEY idx_effective_date (effective_date),
  UNIQUE KEY uk_item_gender_grade_version (item_id, gender, grade_level, version, effective_date)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '成绩标准表';

-- 插入示例数据
INSERT INTO sys_score_criterion (item_id, gender, grade_level, excellent_min, excellent_max, good_min, good_max, pass_min, pass_max, fail_min, fail_max, is_reverse, effective_date, version, create_by, create_time, remark) VALUES
-- 身高体重BMI标准 (item_id=1)
(1, 'M', 'freshman', 18.5, 23.9, 17.0, 18.4, 16.0, 16.9, 0, 15.9, '0', '2025-01-01', '1.0', 'admin', NOW(), '男生大一BMI标准'),
(1, 'F', 'freshman', 18.5, 23.9, 17.0, 18.4, 16.0, 16.9, 0, 15.9, '0', '2025-01-01', '1.0', 'admin', NOW(), '女生大一BMI标准'),

-- 肺活量标准 (item_id=2)
(2, 'M', 'freshman', 4800, 9999, 4300, 4799, 3800, 4299, 0, 3799, '0', '2025-01-01', '1.0', 'admin', NOW(), '男生大一肺活量标准(ml)'),
(2, 'F', 'freshman', 3400, 9999, 3000, 3399, 2500, 2999, 0, 2499, '0', '2025-01-01', '1.0', 'admin', NOW(), '女生大一肺活量标准(ml)'),

-- 50米跑标准 (item_id=3)
(3, 'M', 'freshman', 0, 6.7, 6.8, 7.3, 7.4, 8.3, 8.4, 99, '1', '2025-01-01', '1.0', 'admin', NOW(), '男生大一50米跑标准(秒)'),
(3, 'F', 'freshman', 0, 7.5, 7.6, 8.2, 8.3, 9.6, 9.7, 99, '1', '2025-01-01', '1.0', 'admin', NOW(), '女生大一50米跑标准(秒)'),

-- 立定跳远标准 (item_id=4)
(4, 'M', 'freshman', 273, 999, 263, 272, 248, 262, 0, 247, '0', '2025-01-01', '1.0', 'admin', NOW(), '男生大一立定跳远标准(cm)'),
(4, 'F', 'freshman', 207, 999, 197, 206, 179, 196, 0, 178, '0', '2025-01-01', '1.0', 'admin', NOW(), '女生大一立定跳远标准(cm)'),

-- 坐位体前屈标准 (item_id=5)
(5, 'M', 'freshman', 24.9, 999, 21.6, 24.8, 16.1, 21.5, 0, 16.0, '0', '2025-01-01', '1.0', 'admin', NOW(), '男生大一坐位体前屈标准(cm)'),
(5, 'F', 'freshman', 25.8, 999, 23.2, 25.7, 19.5, 23.1, 0, 19.4, '0', '2025-01-01', '1.0', 'admin', NOW(), '女生大一坐位体前屈标准(cm)'),

-- 仰卧起坐标准 (item_id=6, 女生)
(6, 'F', 'freshman', 56, 999, 52, 55, 44, 51, 0, 43, '0', '2025-01-01', '1.0', 'admin', NOW(), '女生大一仰卧起坐标准(个/分钟)'),

-- 引体向上标准 (item_id=7, 男生)
(7, 'M', 'freshman', 19, 999, 17, 18, 12, 16, 0, 11, '0', '2025-01-01', '1.0', 'admin', NOW(), '男生大一引体向上标准(个)'),

-- 800米跑标准 (item_id=8, 女生)
(8, 'F', 'freshman', 0, 208, 209, 220, 221, 274, 275, 999, '1', '2025-01-01', '1.0', 'admin', NOW(), '女生大一800米跑标准(秒)'),

-- 1000米跑标准 (item_id=9, 男生)
(9, 'M', 'freshman', 0, 207, 208, 220, 221, 274, 275, 999, '1', '2025-01-01', '1.0', 'admin', NOW(), '男生大一1000米跑标准(秒)');
