-- 学生预约管理菜单权限SQL
-- 创建学生预约管理菜单

-- 主菜单：学生预约
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(5000, '学生预约', '0', '6', '/system/studentReservation', '', 'C', '0', '1', 'system:studentReservation:view', 'fa fa-calendar', 'admin', now(), '学生预约管理');

-- 功能按钮
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(5001, '学生预约查询', '5000', '1', '#', '', 'F', '0', '1', 'system:studentReservation:list', '#', 'admin', now(), ''),
(5002, '学生预约详情', '5000', '2', '#', '', 'F', '0', '1', 'system:studentReservation:detail', '#', 'admin', now(), ''),
(5003, '学生预约', '5000', '3', '#', '', 'F', '0', '1', 'system:studentReservation:reserve', '#', 'admin', now(), ''),
(5004, '我的预约', '5000', '4', '#', '', 'F', '0', '1', 'system:studentReservation:myList', '#', 'admin', now(), ''),
(5005, '取消预约', '5000', '5', '#', '', 'F', '0', '1', 'system:studentReservation:cancel', '#', 'admin', now(), '');

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, 4000), 
(1, 4001), 
(1, 4002), 
(1, 4003), 
(1, 4004), 
(1, 4005);

-- 创建学生角色（如果不存在）
INSERT IGNORE INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
(100, '学生', 'student', '3', '5', '1', '1', '0', '0', 'admin', now(), '学生角色');

-- 为学生角色分配学生预约权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(100, 4000), 
(100, 4001), 
(100, 4002), 
(100, 4003), 
(100, 4004), 
(100, 4005);

-- 创建预约类型字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) VALUES
(100, '预约类型', 'reservation_type', '0', 'admin', now(), '预约类型列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(100, 1, '体能测试', 'fitness_test', 'reservation_type', '', 'primary', 'Y', '0', 'admin', now(), '体能测试预约'),
(101, 2, '体质测试', 'physical_test', 'reservation_type', '', 'info', 'N', '0', 'admin', now(), '体质测试预约'),
(102, 3, '补测', 'makeup_test', 'reservation_type', '', 'warning', 'N', '0', 'admin', now(), '补测预约'),
(103, 4, '免测', 'exemption_test', 'reservation_type', '', 'success', 'N', '0', 'admin', now(), '免测预约');

-- 查询验证
SELECT menu_id, menu_name, perms FROM sys_menu WHERE perms LIKE '%studentReservation%' ORDER BY menu_id;
