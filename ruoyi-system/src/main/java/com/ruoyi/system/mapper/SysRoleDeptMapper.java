package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysRoleDept;

/**
 * 角色与院系/班级关联表 数据层
 * 
 * <AUTHOR>
 */
public interface SysRoleDeptMapper
{
    /**
     * 通过角色ID删除角色和院系/班级关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleDeptByRoleId(Long roleId);

    /**
     * 批量删除角色院系/班级关联信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleDept(Long[] ids);

    /**
     * 查询院系/班级使用数量
     * 
     * @param deptId 院系/班级ID
     * @return 结果
     */
    public int selectCountRoleDeptByDeptId(Long deptId);

    /**
     * 批量新增角色院系/班级信息
     * 
     * @param roleDeptList 角色院系/班级列表
     * @return 结果
     */
    public int batchRoleDept(List<SysRoleDept> roleDeptList);
}
