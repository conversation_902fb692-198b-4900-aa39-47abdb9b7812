package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysScoreCriterion;

/**
 * 成绩标准Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface SysScoreCriterionMapper 
{
    /**
     * 查询成绩标准
     * 
     * @param criterionId 成绩标准主键
     * @return 成绩标准
     */
    public SysScoreCriterion selectSysScoreCriterionByCriterionId(Long criterionId);

    /**
     * 查询成绩标准列表
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 成绩标准集合
     */
    public List<SysScoreCriterion> selectSysScoreCriterionList(SysScoreCriterion sysScoreCriterion);

    /**
     * 新增成绩标准
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 结果
     */
    public int insertSysScoreCriterion(SysScoreCriterion sysScoreCriterion);

    /**
     * 修改成绩标准
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 结果
     */
    public int updateSysScoreCriterion(SysScoreCriterion sysScoreCriterion);

    /**
     * 删除成绩标准
     * 
     * @param criterionId 成绩标准主键
     * @return 结果
     */
    public int deleteSysScoreCriterionByCriterionId(Long criterionId);

    /**
     * 批量删除成绩标准
     * 
     * @param criterionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysScoreCriterionByCriterionIds(String[] criterionIds);
}
