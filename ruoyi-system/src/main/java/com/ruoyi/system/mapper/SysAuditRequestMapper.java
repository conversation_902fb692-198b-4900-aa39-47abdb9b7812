package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysAuditRequest;

/**
 * 审核申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface SysAuditRequestMapper 
{
    /**
     * 查询审核申请
     * 
     * @param requestId 审核申请主键
     * @return 审核申请
     */
    public SysAuditRequest selectSysAuditRequestByRequestId(Long requestId);

    /**
     * 查询审核申请列表
     * 
     * @param sysAuditRequest 审核申请
     * @return 审核申请集合
     */
    public List<SysAuditRequest> selectSysAuditRequestList(SysAuditRequest sysAuditRequest);

    /**
     * 新增审核申请
     * 
     * @param sysAuditRequest 审核申请
     * @return 结果
     */
    public int insertSysAuditRequest(SysAuditRequest sysAuditRequest);

    /**
     * 修改审核申请
     * 
     * @param sysAuditRequest 审核申请
     * @return 结果
     */
    public int updateSysAuditRequest(SysAuditRequest sysAuditRequest);

    /**
     * 删除审核申请
     * 
     * @param requestId 审核申请主键
     * @return 结果
     */
    public int deleteSysAuditRequestByRequestId(Long requestId);

    /**
     * 批量删除审核申请
     * 
     * @param requestIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAuditRequestByRequestIds(String[] requestIds);
}
