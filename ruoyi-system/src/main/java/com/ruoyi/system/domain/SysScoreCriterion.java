package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 成绩标准对象 sys_score_criterion
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public class SysScoreCriterion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标准ID */
    private Long criterionId;

    /** 考核项目ID */
    @Excel(name = "考核项目ID")
    private Long itemId;

    /** 性别(M男F女) */
    private String gender;

    /** 年级(freshman,sophomore,junior,senior) */
    private String gradeLevel;

    /** 优秀最低值 */
    private BigDecimal excellentMin;

    /** 优秀最高值 */
    private BigDecimal excellentMax;

    /** 良好最低值 */
    private BigDecimal goodMin;

    /** 良好最高值 */
    private BigDecimal goodMax;

    /** 及格最低值 */
    private BigDecimal passMin;

    /** 及格最高值 */
    private BigDecimal passMax;

    /** 不及格最低值 */
    private BigDecimal failMin;

    /** 不及格最高值 */
    private BigDecimal failMax;

    /** 是否反向计算(0正向1反向,如跑步时间越少越好) */
    private String isReverse;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    public void setCriterionId(Long criterionId) 
    {
        this.criterionId = criterionId;
    }

    public Long getCriterionId() 
    {
        return criterionId;
    }

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }

    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }

    public void setGradeLevel(String gradeLevel) 
    {
        this.gradeLevel = gradeLevel;
    }

    public String getGradeLevel() 
    {
        return gradeLevel;
    }

    public void setExcellentMin(BigDecimal excellentMin) 
    {
        this.excellentMin = excellentMin;
    }

    public BigDecimal getExcellentMin() 
    {
        return excellentMin;
    }

    public void setExcellentMax(BigDecimal excellentMax) 
    {
        this.excellentMax = excellentMax;
    }

    public BigDecimal getExcellentMax() 
    {
        return excellentMax;
    }

    public void setGoodMin(BigDecimal goodMin) 
    {
        this.goodMin = goodMin;
    }

    public BigDecimal getGoodMin() 
    {
        return goodMin;
    }

    public void setGoodMax(BigDecimal goodMax) 
    {
        this.goodMax = goodMax;
    }

    public BigDecimal getGoodMax() 
    {
        return goodMax;
    }

    public void setPassMin(BigDecimal passMin) 
    {
        this.passMin = passMin;
    }

    public BigDecimal getPassMin() 
    {
        return passMin;
    }

    public void setPassMax(BigDecimal passMax) 
    {
        this.passMax = passMax;
    }

    public BigDecimal getPassMax() 
    {
        return passMax;
    }

    public void setFailMin(BigDecimal failMin) 
    {
        this.failMin = failMin;
    }

    public BigDecimal getFailMin() 
    {
        return failMin;
    }

    public void setFailMax(BigDecimal failMax) 
    {
        this.failMax = failMax;
    }

    public BigDecimal getFailMax() 
    {
        return failMax;
    }

    public void setIsReverse(String isReverse) 
    {
        this.isReverse = isReverse;
    }

    public String getIsReverse() 
    {
        return isReverse;
    }

    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }

    public void setExpireDate(Date expireDate) 
    {
        this.expireDate = expireDate;
    }

    public Date getExpireDate() 
    {
        return expireDate;
    }

    public void setVersion(String version) 
    {
        this.version = version;
    }

    public String getVersion() 
    {
        return version;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("criterionId", getCriterionId())
            .append("itemId", getItemId())
            .append("gender", getGender())
            .append("gradeLevel", getGradeLevel())
            .append("excellentMin", getExcellentMin())
            .append("excellentMax", getExcellentMax())
            .append("goodMin", getGoodMin())
            .append("goodMax", getGoodMax())
            .append("passMin", getPassMin())
            .append("passMax", getPassMax())
            .append("failMin", getFailMin())
            .append("failMax", getFailMax())
            .append("isReverse", getIsReverse())
            .append("effectiveDate", getEffectiveDate())
            .append("expireDate", getExpireDate())
            .append("version", getVersion())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
