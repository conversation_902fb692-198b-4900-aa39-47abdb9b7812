package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysReservationMapper;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.common.core.text.Convert;

/**
 * 预约管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class SysReservationServiceImpl implements ISysReservationService 
{
    @Autowired
    private SysReservationMapper sysReservationMapper;

    /**
     * 查询预约管理
     * 
     * @param reservationId 预约管理主键
     * @return 预约管理
     */
    @Override
    public SysReservation selectSysReservationByReservationId(Long reservationId)
    {
        return sysReservationMapper.selectSysReservationByReservationId(reservationId);
    }

    /**
     * 查询预约管理列表
     * 
     * @param sysReservation 预约管理
     * @return 预约管理
     */
    @Override
    public List<SysReservation> selectSysReservationList(SysReservation sysReservation)
    {
        return sysReservationMapper.selectSysReservationList(sysReservation);
    }

    /**
     * 新增预约管理
     * 
     * @param sysReservation 预约管理
     * @return 结果
     */
    @Override
    public int insertSysReservation(SysReservation sysReservation)
    {
        sysReservation.setCreateTime(DateUtils.getNowDate());
        return sysReservationMapper.insertSysReservation(sysReservation);
    }

    /**
     * 修改预约管理
     * 
     * @param sysReservation 预约管理
     * @return 结果
     */
    @Override
    public int updateSysReservation(SysReservation sysReservation)
    {
        sysReservation.setUpdateTime(DateUtils.getNowDate());
        return sysReservationMapper.updateSysReservation(sysReservation);
    }

    /**
     * 批量删除预约管理
     * 
     * @param reservationIds 需要删除的预约管理主键
     * @return 结果
     */
    @Override
    public int deleteSysReservationByReservationIds(String reservationIds)
    {
        return sysReservationMapper.deleteSysReservationByReservationIds(Convert.toStrArray(reservationIds));
    }

    /**
     * 删除预约管理信息
     * 
     * @param reservationId 预约管理主键
     * @return 结果
     */
    @Override
    public int deleteSysReservationByReservationId(Long reservationId)
    {
        return sysReservationMapper.deleteSysReservationByReservationId(reservationId);
    }
}
