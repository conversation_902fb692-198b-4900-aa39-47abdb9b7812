package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;

/**
 * 院系/班级管理 服务层
 * 
 * <AUTHOR>
 */
public interface ISysDeptService
{
    /**
     * 查询院系/班级管理数据
     * 
     * @param dept 院系/班级信息
     * @return 院系/班级信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询院系/班级管理树
     * 
     * @param dept 院系/班级信息
     * @return 所有院系/班级信息
     */
    public List<Ztree> selectDeptTree(SysDept dept);

    /**
     * 查询院系/班级管理树（排除下级）
     * 
     * @param dept 院系/班级信息
     * @return 所有院系/班级信息
     */
    public List<Ztree> selectDeptTreeExcludeChild(SysDept dept);

    /**
     * 根据角色ID查询菜单
     *
     * @param role 角色对象
     * @return 菜单列表
     */
    public List<Ztree> roleDeptTreeData(SysRole role);

    /**
     * 根据父院系/班级ID查询下级院系/班级数量
     * 
     * @param parentId 父院系/班级ID
     * @return 结果
     */
    public int selectDeptCount(Long parentId);

    /**
     * 查询院系/班级是否存在用户
     * 
     * @param deptId 院系/班级ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 删除院系/班级管理信息
     * 
     * @param deptId 院系/班级ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 新增保存院系/班级信息
     * 
     * @param dept 院系/班级信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存院系/班级信息
     * 
     * @param dept 院系/班级信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 根据院系/班级ID查询信息
     * 
     * @param deptId 院系/班级ID
     * @return 院系/班级信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子院系/班级（正常状态）
     * 
     * @param deptId 院系/班级ID
     * @return 子院系/班级数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 校验院系/班级名称是否唯一
     * 
     * @param dept 院系/班级信息
     * @return 结果
     */
    public boolean checkDeptNameUnique(SysDept dept);

    /**
     * 校验院系/班级是否有数据权限
     * 
     * @param deptId 院系/班级id
     */
    public void checkDeptDataScope(Long deptId);
}
