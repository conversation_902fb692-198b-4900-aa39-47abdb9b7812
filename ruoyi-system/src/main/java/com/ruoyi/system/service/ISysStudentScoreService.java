package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysStudentScore;

/**
 * 学生成绩Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ISysStudentScoreService 
{
    /**
     * 查询学生成绩
     * 
     * @param scoreId 学生成绩主键
     * @return 学生成绩
     */
    public SysStudentScore selectSysStudentScoreByScoreId(Long scoreId);

    /**
     * 查询学生成绩列表
     * 
     * @param sysStudentScore 学生成绩
     * @return 学生成绩集合
     */
    public List<SysStudentScore> selectSysStudentScoreList(SysStudentScore sysStudentScore);

    /**
     * 新增学生成绩
     * 
     * @param sysStudentScore 学生成绩
     * @return 结果
     */
    public int insertSysStudentScore(SysStudentScore sysStudentScore);

    /**
     * 修改学生成绩
     * 
     * @param sysStudentScore 学生成绩
     * @return 结果
     */
    public int updateSysStudentScore(SysStudentScore sysStudentScore);

    /**
     * 批量删除学生成绩
     * 
     * @param scoreIds 需要删除的学生成绩主键集合
     * @return 结果
     */
    public int deleteSysStudentScoreByScoreIds(String scoreIds);

    /**
     * 删除学生成绩信息
     * 
     * @param scoreId 学生成绩主键
     * @return 结果
     */
    public int deleteSysStudentScoreByScoreId(Long scoreId);
}
