package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysAuditRequestMapper;
import com.ruoyi.system.domain.SysAuditRequest;
import com.ruoyi.system.service.ISysAuditRequestService;
import com.ruoyi.common.core.text.Convert;

/**
 * 审核申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class SysAuditRequestServiceImpl implements ISysAuditRequestService 
{
    @Autowired
    private SysAuditRequestMapper sysAuditRequestMapper;

    /**
     * 查询审核申请
     * 
     * @param requestId 审核申请主键
     * @return 审核申请
     */
    @Override
    public SysAuditRequest selectSysAuditRequestByRequestId(Long requestId)
    {
        return sysAuditRequestMapper.selectSysAuditRequestByRequestId(requestId);
    }

    /**
     * 查询审核申请列表
     * 
     * @param sysAuditRequest 审核申请
     * @return 审核申请
     */
    @Override
    public List<SysAuditRequest> selectSysAuditRequestList(SysAuditRequest sysAuditRequest)
    {
        return sysAuditRequestMapper.selectSysAuditRequestList(sysAuditRequest);
    }

    /**
     * 新增审核申请
     * 
     * @param sysAuditRequest 审核申请
     * @return 结果
     */
    @Override
    public int insertSysAuditRequest(SysAuditRequest sysAuditRequest)
    {
        sysAuditRequest.setCreateTime(DateUtils.getNowDate());
        return sysAuditRequestMapper.insertSysAuditRequest(sysAuditRequest);
    }

    /**
     * 修改审核申请
     * 
     * @param sysAuditRequest 审核申请
     * @return 结果
     */
    @Override
    public int updateSysAuditRequest(SysAuditRequest sysAuditRequest)
    {
        sysAuditRequest.setUpdateTime(DateUtils.getNowDate());
        return sysAuditRequestMapper.updateSysAuditRequest(sysAuditRequest);
    }

    /**
     * 批量删除审核申请
     * 
     * @param requestIds 需要删除的审核申请主键
     * @return 结果
     */
    @Override
    public int deleteSysAuditRequestByRequestIds(String requestIds)
    {
        return sysAuditRequestMapper.deleteSysAuditRequestByRequestIds(Convert.toStrArray(requestIds));
    }

    /**
     * 删除审核申请信息
     * 
     * @param requestId 审核申请主键
     * @return 结果
     */
    @Override
    public int deleteSysAuditRequestByRequestId(Long requestId)
    {
        return sysAuditRequestMapper.deleteSysAuditRequestByRequestId(requestId);
    }
}
