package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysStudentScoreMapper;
import com.ruoyi.system.domain.SysStudentScore;
import com.ruoyi.system.service.ISysStudentScoreService;
import com.ruoyi.common.core.text.Convert;

/**
 * 学生成绩Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class SysStudentScoreServiceImpl implements ISysStudentScoreService 
{
    @Autowired
    private SysStudentScoreMapper sysStudentScoreMapper;

    /**
     * 查询学生成绩
     * 
     * @param scoreId 学生成绩主键
     * @return 学生成绩
     */
    @Override
    public SysStudentScore selectSysStudentScoreByScoreId(Long scoreId)
    {
        return sysStudentScoreMapper.selectSysStudentScoreByScoreId(scoreId);
    }

    /**
     * 查询学生成绩列表
     * 
     * @param sysStudentScore 学生成绩
     * @return 学生成绩
     */
    @Override
    public List<SysStudentScore> selectSysStudentScoreList(SysStudentScore sysStudentScore)
    {
        return sysStudentScoreMapper.selectSysStudentScoreList(sysStudentScore);
    }

    /**
     * 新增学生成绩
     *
     * @param sysStudentScore 学生成绩
     * @return 结果
     */
    @Override
    public int insertSysStudentScore(SysStudentScore sysStudentScore)
    {
        // 设置创建时间
        sysStudentScore.setCreateTime(DateUtils.getNowDate());

        // 如果 itemId 为空，设置默认值或抛出异常
        if (sysStudentScore.getItemId() == null) {
            // 可以设置一个默认的项目ID，或者抛出异常要求必须提供
            // 这里我们抛出异常，要求调用方必须提供 itemId
            throw new RuntimeException("考核项目ID不能为空");
        }

        return sysStudentScoreMapper.insertSysStudentScore(sysStudentScore);
    }

    /**
     * 修改学生成绩
     * 
     * @param sysStudentScore 学生成绩
     * @return 结果
     */
    @Override
    public int updateSysStudentScore(SysStudentScore sysStudentScore)
    {
        sysStudentScore.setUpdateTime(DateUtils.getNowDate());
        return sysStudentScoreMapper.updateSysStudentScore(sysStudentScore);
    }

    /**
     * 批量删除学生成绩
     * 
     * @param scoreIds 需要删除的学生成绩主键
     * @return 结果
     */
    @Override
    public int deleteSysStudentScoreByScoreIds(String scoreIds)
    {
        return sysStudentScoreMapper.deleteSysStudentScoreByScoreIds(Convert.toStrArray(scoreIds));
    }

    /**
     * 删除学生成绩信息
     * 
     * @param scoreId 学生成绩主键
     * @return 结果
     */
    @Override
    public int deleteSysStudentScoreByScoreId(Long scoreId)
    {
        return sysStudentScoreMapper.deleteSysStudentScoreByScoreId(scoreId);
    }
}
