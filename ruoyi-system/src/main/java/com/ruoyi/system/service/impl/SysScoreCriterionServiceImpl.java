package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysScoreCriterionMapper;
import com.ruoyi.system.domain.SysScoreCriterion;
import com.ruoyi.system.service.ISysScoreCriterionService;
import com.ruoyi.common.core.text.Convert;

/**
 * 成绩标准Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class SysScoreCriterionServiceImpl implements ISysScoreCriterionService 
{
    @Autowired
    private SysScoreCriterionMapper sysScoreCriterionMapper;

    /**
     * 查询成绩标准
     * 
     * @param criterionId 成绩标准主键
     * @return 成绩标准
     */
    @Override
    public SysScoreCriterion selectSysScoreCriterionByCriterionId(Long criterionId)
    {
        return sysScoreCriterionMapper.selectSysScoreCriterionByCriterionId(criterionId);
    }

    /**
     * 查询成绩标准列表
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 成绩标准
     */
    @Override
    public List<SysScoreCriterion> selectSysScoreCriterionList(SysScoreCriterion sysScoreCriterion)
    {
        return sysScoreCriterionMapper.selectSysScoreCriterionList(sysScoreCriterion);
    }

    /**
     * 新增成绩标准
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 结果
     */
    @Override
    public int insertSysScoreCriterion(SysScoreCriterion sysScoreCriterion)
    {
        sysScoreCriterion.setCreateTime(DateUtils.getNowDate());
        return sysScoreCriterionMapper.insertSysScoreCriterion(sysScoreCriterion);
    }

    /**
     * 修改成绩标准
     * 
     * @param sysScoreCriterion 成绩标准
     * @return 结果
     */
    @Override
    public int updateSysScoreCriterion(SysScoreCriterion sysScoreCriterion)
    {
        sysScoreCriterion.setUpdateTime(DateUtils.getNowDate());
        return sysScoreCriterionMapper.updateSysScoreCriterion(sysScoreCriterion);
    }

    /**
     * 批量删除成绩标准
     * 
     * @param criterionIds 需要删除的成绩标准主键
     * @return 结果
     */
    @Override
    public int deleteSysScoreCriterionByCriterionIds(String criterionIds)
    {
        return sysScoreCriterionMapper.deleteSysScoreCriterionByCriterionIds(Convert.toStrArray(criterionIds));
    }

    /**
     * 删除成绩标准信息
     * 
     * @param criterionId 成绩标准主键
     * @return 结果
     */
    @Override
    public int deleteSysScoreCriterionByCriterionId(Long criterionId)
    {
        return sysScoreCriterionMapper.deleteSysScoreCriterionByCriterionId(criterionId);
    }
}
