<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysScoreCriterionMapper">
    
    <resultMap type="SysScoreCriterion" id="SysScoreCriterionResult">
        <result property="criterionId"    column="criterion_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="gender"    column="gender"    />
        <result property="gradeLevel"    column="grade_level"    />
        <result property="excellentMin"    column="excellent_min"    />
        <result property="excellentMax"    column="excellent_max"    />
        <result property="goodMin"    column="good_min"    />
        <result property="goodMax"    column="good_max"    />
        <result property="passMin"    column="pass_min"    />
        <result property="passMax"    column="pass_max"    />
        <result property="failMin"    column="fail_min"    />
        <result property="failMax"    column="fail_max"    />
        <result property="isReverse"    column="is_reverse"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="version"    column="version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysScoreCriterionVo">
        select criterion_id, item_id, gender, grade_level, excellent_min, excellent_max, good_min, good_max, pass_min, pass_max, fail_min, fail_max, is_reverse, effective_date, expire_date, version, create_by, create_time, update_by, update_time, remark from sys_score_criterion
    </sql>

    <select id="selectSysScoreCriterionList" parameterType="SysScoreCriterion" resultMap="SysScoreCriterionResult">
        <include refid="selectSysScoreCriterionVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
        </where>
    </select>
    
    <select id="selectSysScoreCriterionByCriterionId" parameterType="Long" resultMap="SysScoreCriterionResult">
        <include refid="selectSysScoreCriterionVo"/>
        where criterion_id = #{criterionId}
    </select>

    <insert id="insertSysScoreCriterion" parameterType="SysScoreCriterion" useGeneratedKeys="true" keyProperty="criterionId">
        insert into sys_score_criterion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="gradeLevel != null and gradeLevel != ''">grade_level,</if>
            <if test="excellentMin != null">excellent_min,</if>
            <if test="excellentMax != null">excellent_max,</if>
            <if test="goodMin != null">good_min,</if>
            <if test="goodMax != null">good_max,</if>
            <if test="passMin != null">pass_min,</if>
            <if test="passMax != null">pass_max,</if>
            <if test="failMin != null">fail_min,</if>
            <if test="failMax != null">fail_max,</if>
            <if test="isReverse != null">is_reverse,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="version != null">version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="gradeLevel != null and gradeLevel != ''">#{gradeLevel},</if>
            <if test="excellentMin != null">#{excellentMin},</if>
            <if test="excellentMax != null">#{excellentMax},</if>
            <if test="goodMin != null">#{goodMin},</if>
            <if test="goodMax != null">#{goodMax},</if>
            <if test="passMin != null">#{passMin},</if>
            <if test="passMax != null">#{passMax},</if>
            <if test="failMin != null">#{failMin},</if>
            <if test="failMax != null">#{failMax},</if>
            <if test="isReverse != null">#{isReverse},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="version != null">#{version},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysScoreCriterion" parameterType="SysScoreCriterion">
        update sys_score_criterion
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="gradeLevel != null and gradeLevel != ''">grade_level = #{gradeLevel},</if>
            <if test="excellentMin != null">excellent_min = #{excellentMin},</if>
            <if test="excellentMax != null">excellent_max = #{excellentMax},</if>
            <if test="goodMin != null">good_min = #{goodMin},</if>
            <if test="goodMax != null">good_max = #{goodMax},</if>
            <if test="passMin != null">pass_min = #{passMin},</if>
            <if test="passMax != null">pass_max = #{passMax},</if>
            <if test="failMin != null">fail_min = #{failMin},</if>
            <if test="failMax != null">fail_max = #{failMax},</if>
            <if test="isReverse != null">is_reverse = #{isReverse},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="version != null">version = #{version},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where criterion_id = #{criterionId}
    </update>

    <delete id="deleteSysScoreCriterionByCriterionId" parameterType="Long">
        delete from sys_score_criterion where criterion_id = #{criterionId}
    </delete>

    <delete id="deleteSysScoreCriterionByCriterionIds" parameterType="String">
        delete from sys_score_criterion where criterion_id in 
        <foreach item="criterionId" collection="array" open="(" separator="," close=")">
            #{criterionId}
        </foreach>
    </delete>

</mapper>