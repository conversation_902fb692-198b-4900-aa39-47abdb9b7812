<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysAuditRequestMapper">
    
    <resultMap type="SysAuditRequest" id="SysAuditRequestResult">
        <result property="requestId"    column="request_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="studentNo"    column="student_no"    />
        <result property="requestType"    column="request_type"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="requestReason"    column="request_reason"    />
        <result property="description"    column="description"    />
        <result property="attachmentUrls"    column="attachment_urls"    />
        <result property="requestStatus"    column="request_status"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditComment"    column="audit_comment"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAuditRequestVo">
        select request_id, student_id, student_name, student_no, request_type, subject_name, request_reason, description, attachment_urls, request_status, submit_time, audit_user_name, audit_time, audit_comment, create_by, create_time, update_by, update_time, remark from sys_audit_request
    </sql>

    <select id="selectSysAuditRequestList" parameterType="SysAuditRequest" resultMap="SysAuditRequestResult">
        <include refid="selectSysAuditRequestVo"/>
        <where>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentNo != null  and studentNo != ''"> and student_no = #{studentNo}</if>
            <if test="requestType != null  and requestType != ''"> and request_type = #{requestType}</if>
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="requestReason != null  and requestReason != ''"> and request_reason = #{requestReason}</if>
            <if test="requestStatus != null  and requestStatus != ''"> and request_status = #{requestStatus}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="params.beginSubmitTime != null and params.beginSubmitTime != ''"><!-- 开始提交时间检索 -->
                and date_format(submit_time,'%y%m%d') &gt;= date_format(#{params.beginSubmitTime},'%y%m%d')
            </if>
            <if test="params.endSubmitTime != null and params.endSubmitTime != ''"><!-- 结束提交时间检索 -->
                and date_format(submit_time,'%y%m%d') &lt;= date_format(#{params.endSubmitTime},'%y%m%d')
            </if>
        </where>
        order by submit_time desc
    </select>
    
    <select id="selectSysAuditRequestByRequestId" parameterType="Long" resultMap="SysAuditRequestResult">
        <include refid="selectSysAuditRequestVo"/>
        where request_id = #{requestId}
    </select>

    <insert id="insertSysAuditRequest" parameterType="SysAuditRequest" useGeneratedKeys="true" keyProperty="requestId">
        insert into sys_audit_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="studentName != null">student_name,</if>
            <if test="studentNo != null and studentNo != ''">student_no,</if>
            <if test="requestType != null and requestType != ''">request_type,</if>
            <if test="subjectName != null and subjectName != ''">subject_name,</if>
            <if test="requestReason != null and requestReason != ''">request_reason,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="attachmentUrls != null">attachment_urls,</if>
            <if test="requestStatus != null">request_status,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditComment != null">audit_comment,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="studentName != null">#{studentName},</if>
            <if test="studentNo != null and studentNo != ''">#{studentNo},</if>
            <if test="requestType != null and requestType != ''">#{requestType},</if>
            <if test="subjectName != null and subjectName != ''">#{subjectName},</if>
            <if test="requestReason != null and requestReason != ''">#{requestReason},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="attachmentUrls != null">#{attachmentUrls},</if>
            <if test="requestStatus != null">#{requestStatus},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditComment != null">#{auditComment},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysAuditRequest" parameterType="SysAuditRequest">
        update sys_audit_request
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="studentName != null">student_name = #{studentName},</if>
            <if test="studentNo != null and studentNo != ''">student_no = #{studentNo},</if>
            <if test="requestType != null and requestType != ''">request_type = #{requestType},</if>
            <if test="subjectName != null and subjectName != ''">subject_name = #{subjectName},</if>
            <if test="requestReason != null and requestReason != ''">request_reason = #{requestReason},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="attachmentUrls != null">attachment_urls = #{attachmentUrls},</if>
            <if test="requestStatus != null">request_status = #{requestStatus},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditComment != null">audit_comment = #{auditComment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where request_id = #{requestId}
    </update>

    <delete id="deleteSysAuditRequestByRequestId" parameterType="Long">
        delete from sys_audit_request where request_id = #{requestId}
    </delete>

    <delete id="deleteSysAuditRequestByRequestIds" parameterType="String">
        delete from sys_audit_request where request_id in 
        <foreach item="requestId" collection="array" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </delete>

</mapper>