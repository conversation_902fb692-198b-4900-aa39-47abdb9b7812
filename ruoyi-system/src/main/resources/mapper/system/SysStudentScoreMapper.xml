<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysStudentScoreMapper">
    
    <resultMap type="SysStudentScore" id="SysStudentScoreResult">
        <result property="scoreId"    column="score_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="criterionId"    column="criterion_id"    />
        <result property="rawScore"    column="raw_score"    />
        <result property="gradeScore"    column="grade_score"    />
        <result property="scorePoints"    column="score_points"    />
        <result property="examDate"    column="exam_date"    />
        <result property="examSemester"    column="exam_semester"    />
        <result property="examYear"    column="exam_year"    />
        <result property="importBatch"    column="import_batch"    />
        <result property="isManual"    column="is_manual"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysStudentScoreVo">
        select score_id, student_id, item_id, criterion_id, raw_score, grade_score, score_points, exam_date, exam_semester, exam_year, import_batch, is_manual, status, create_by, create_time, update_by, update_time, remark from sys_student_score
    </sql>

    <select id="selectSysStudentScoreList" parameterType="SysStudentScore" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        <where>  
            <if test="examDate != null "> and exam_date = #{examDate}</if>
            <if test="examSemester != null  and examSemester != ''"> and exam_semester = #{examSemester}</if>
            <if test="examYear != null "> and exam_year = #{examYear}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysStudentScoreByScoreId" parameterType="Long" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        where score_id = #{scoreId}
    </select>

    <insert id="insertSysStudentScore" parameterType="SysStudentScore" useGeneratedKeys="true" keyProperty="scoreId">
        insert into sys_student_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="criterionId != null">criterion_id,</if>
            <if test="rawScore != null">raw_score,</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score,</if>
            <if test="scorePoints != null">score_points,</if>
            <if test="examDate != null">exam_date,</if>
            <if test="examSemester != null and examSemester != ''">exam_semester,</if>
            <if test="examYear != null">exam_year,</if>
            <if test="importBatch != null">import_batch,</if>
            <if test="isManual != null">is_manual,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="criterionId != null">#{criterionId},</if>
            <if test="rawScore != null">#{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">#{gradeScore},</if>
            <if test="scorePoints != null">#{scorePoints},</if>
            <if test="examDate != null">#{examDate},</if>
            <if test="examSemester != null and examSemester != ''">#{examSemester},</if>
            <if test="examYear != null">#{examYear},</if>
            <if test="importBatch != null">#{importBatch},</if>
            <if test="isManual != null">#{isManual},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysStudentScore" parameterType="SysStudentScore">
        update sys_student_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="criterionId != null">criterion_id = #{criterionId},</if>
            <if test="rawScore != null">raw_score = #{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score = #{gradeScore},</if>
            <if test="scorePoints != null">score_points = #{scorePoints},</if>
            <if test="examDate != null">exam_date = #{examDate},</if>
            <if test="examSemester != null and examSemester != ''">exam_semester = #{examSemester},</if>
            <if test="examYear != null">exam_year = #{examYear},</if>
            <if test="importBatch != null">import_batch = #{importBatch},</if>
            <if test="isManual != null">is_manual = #{isManual},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where score_id = #{scoreId}
    </update>

    <delete id="deleteSysStudentScoreByScoreId" parameterType="Long">
        delete from sys_student_score where score_id = #{scoreId}
    </delete>

    <delete id="deleteSysStudentScoreByScoreIds" parameterType="String">
        delete from sys_student_score where score_id in 
        <foreach item="scoreId" collection="array" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
    </delete>

</mapper>