<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('审核申请列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentNo"/>
                            </li>
                            <li>
                                <label>申请科目名称：</label>
                                <input type="text" name="subjectName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:request:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:request:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:request:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:request:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:request:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:request:remove')}]];
        var prefix = ctx + "system/request";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "审核申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'requestId',
                    title: '申请ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名'
                },
                {
                    field: 'studentNo',
                    title: '学号'
                },
                {
                    field: 'requestType',
                    title: '申请类型(defer:缓考,exempt:免考)'
                },
                {
                    field: 'subjectName',
                    title: '申请科目名称'
                },
                {
                    field: 'requestReason',
                    title: '申请原因'
                },
                {
                    field: 'description',
                    title: '详细说明描述'
                },
                {
                    field: 'attachmentUrls',
                    title: '附件URL列表(多个URL用逗号分隔)'
                },
                {
                    field: 'requestStatus',
                    title: '申请状态(pending:待审核,approved:审核通过,rejected:审核驳回)'
                },
                {
                    field: 'submitTime',
                    title: '提交时间'
                },
                {
                    field: 'auditUserName',
                    title: '审核人姓名'
                },
                {
                    field: 'auditTime',
                    title: '审核时间'
                },
                {
                    field: 'auditComment',
                    title: '审核意见/驳回理由'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.requestId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.requestId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>