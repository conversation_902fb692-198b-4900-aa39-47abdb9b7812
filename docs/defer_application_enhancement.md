# 缓考申请页面增强功能开发总结

## 🎯 需求分析

根据您的要求，对缓考申请页面进行以下增强：

1. **学生姓名改为下拉选择**：从输入框改为下拉选择框，调用用户接口获取学生列表
2. **只查询学生角色用户**：接口只返回具有学生角色的用户
3. **添加证明材料上传**：添加文件上传控件，调用系统上传接口

## ✅ 已完成的后端开发

### 1. **Controller层增强**

#### 添加依赖注入
```java
@Autowired
private ISysUserService userService;

@Autowired
private ISysRoleService roleService;
```

#### 新增学生列表接口
```java
/**
 * 获取学生用户列表（用于下拉选择）
 */
@PostMapping("/studentList")
@ResponseBody
public AjaxResult getStudentList()
{
    try {
        // 查找学生角色
        List<SysRole> allRoles = roleService.selectRoleAll();
        SysRole studentRole = allRoles.stream()
            .filter(role -> "student".equals(role.getRoleKey()) || "学生".equals(role.getRoleName()))
            .findFirst()
            .orElse(null);
        
        if (studentRole != null) {
            // 使用selectAllocatedList查询已分配学生角色的用户
            SysUser queryUser = new SysUser();
            queryUser.setRoleId(studentRole.getRoleId());
            List<SysUser> studentUsers = userService.selectAllocatedList(queryUser);
            return AjaxResult.success(studentUsers);
        } else {
            // 如果没有找到学生角色，返回所有用户（兼容处理）
            SysUser queryUser = new SysUser();
            List<SysUser> allUsers = userService.selectUserList(queryUser);
            return AjaxResult.success(allUsers);
        }
    } catch (Exception e) {
        return AjaxResult.error("获取学生列表失败：" + e.getMessage());
    }
}
```

### 2. **接口特性**

#### 智能角色识别
- 自动查找角色key为"student"或角色名为"学生"的角色
- 使用`selectAllocatedList`方法精确查询该角色的用户
- 兼容处理：如果没有找到学生角色，返回所有用户

#### 异常处理
- 完整的try-catch异常处理
- 友好的错误信息返回

## 🔧 需要完成的前端开发

### 1. **修改缓考申请新增页面**

#### 学生姓名下拉选择
```html
<!-- 原来的输入框 -->
<input name="studentName" class="form-control" type="text" required placeholder="请输入学生姓名">

<!-- 修改为下拉选择 -->
<select name="studentName" class="form-control" required>
    <option value="">请选择学生</option>
</select>
```

#### 添加证明材料上传
```html
<div class="col-xs-12">
    <div class="form-group">
        <label class="col-sm-3 control-label">证明材料：</label>
        <div class="col-sm-9">
            <input type="file" id="evidenceFile" name="evidenceFile" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
            <input type="hidden" name="evidenceUrls" id="evidenceUrls">
            <div class="file-list" id="fileList"></div>
        </div>
    </div>
</div>
```

### 2. **添加JavaScript功能**

#### 加载学生列表
```javascript
// 页面加载时获取学生列表
$(function() {
    loadStudentList();
});

function loadStudentList() {
    $.post(prefix + "/studentList", {}, function(result) {
        if (result.code == 0) {
            var studentSelect = $("select[name='studentName']");
            studentSelect.empty();
            studentSelect.append('<option value="">请选择学生</option>');
            
            $.each(result.data, function(index, user) {
                studentSelect.append('<option value="' + user.userName + '" data-user-id="' + user.userId + '" data-login-name="' + user.loginName + '">' + user.userName + '</option>');
            });
        } else {
            $.modal.msgError("获取学生列表失败：" + result.msg);
        }
    });
}
```

#### 学生选择联动
```javascript
// 学生选择时自动填充学号
$("select[name='studentName']").change(function() {
    var selectedOption = $(this).find("option:selected");
    var loginName = selectedOption.data("login-name");
    if (loginName) {
        $("input[name='studentNo']").val(loginName);
    }
});
```

#### 文件上传功能
```javascript
// 文件上传处理
$("#evidenceFile").change(function() {
    var files = this.files;
    if (files.length > 0) {
        uploadFiles(files);
    }
});

function uploadFiles(files) {
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append("files", files[i]);
    }
    
    $.ajax({
        url: ctx + "common/uploads",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        success: function(result) {
            if (result.code == 0) {
                // 保存文件URL
                var urls = result.urls.join(",");
                $("#evidenceUrls").val(urls);
                
                // 显示文件列表
                showFileList(result.fileNames, result.originalFilenames);
                $.modal.msgSuccess("文件上传成功");
            } else {
                $.modal.msgError("文件上传失败：" + result.msg);
            }
        },
        error: function() {
            $.modal.msgError("文件上传失败");
        }
    });
}

function showFileList(fileNames, originalNames) {
    var fileListHtml = "";
    for (var i = 0; i < fileNames.length; i++) {
        fileListHtml += '<div class="file-item">';
        fileListHtml += '<span class="file-name">' + originalNames[i] + '</span>';
        fileListHtml += '<a href="' + ctx + 'common/download?fileName=' + fileNames[i] + '" class="btn btn-xs btn-info">下载</a>';
        fileListHtml += '</div>';
    }
    $("#fileList").html(fileListHtml);
}
```

## 🗄️ 数据库字段扩展

### 需要在sys_audit_request表中添加证明材料字段

```sql
ALTER TABLE sys_audit_request ADD COLUMN evidence_urls TEXT COMMENT '证明材料文件URL，多个用逗号分隔';
```

### 对应的实体类修改

```java
// 在SysAuditRequest类中添加字段
/** 证明材料文件URL */
private String evidenceUrls;

// getter和setter方法
public String getEvidenceUrls() {
    return evidenceUrls;
}

public void setEvidenceUrls(String evidenceUrls) {
    this.evidenceUrls = evidenceUrls;
}
```

## 🔄 系统集成点

### 1. **用户接口集成**
- ✅ 已集成`ISysUserService.selectAllocatedList()`方法
- ✅ 已集成`ISysRoleService.selectRoleAll()`方法
- ✅ 智能识别学生角色

### 2. **文件上传接口集成**
- 🔄 需要调用`/common/uploads`接口（多文件上传）
- 🔄 需要调用`/common/download`接口（文件下载）
- ✅ 系统已有完整的文件上传下载功能

## 📋 完整的开发清单

### ✅ 已完成
1. **后端Controller增强**：添加学生列表查询接口
2. **角色权限集成**：智能识别学生角色
3. **异常处理**：完整的错误处理机制
4. **编译验证**：代码编译通过

### 🔄 待完成
1. **前端页面修改**：
   - 修改add.html页面的学生姓名字段
   - 添加证明材料上传控件
   - 修改edit.html页面（如需要）

2. **JavaScript功能开发**：
   - 学生列表加载功能
   - 学生选择联动功能
   - 文件上传处理功能
   - 文件列表显示功能

3. **数据库扩展**：
   - 添加evidence_urls字段
   - 修改实体类

4. **页面样式优化**：
   - 文件上传控件样式
   - 文件列表显示样式

## 🚀 测试建议

### 1. **接口测试**
```bash
# 测试学生列表接口
curl -X POST http://localhost:8080/system/defer/studentList
```

### 2. **功能测试**
1. 测试学生下拉列表是否正确加载
2. 测试学生选择后学号是否自动填充
3. 测试文件上传功能是否正常
4. 测试文件下载功能是否正常

### 3. **权限测试**
1. 验证只返回学生角色的用户
2. 验证不同角色用户的访问权限

## 📝 使用说明

### 管理员配置
1. 确保系统中有"学生"角色（roleKey="student"或roleName="学生"）
2. 为学生用户分配学生角色
3. 配置文件上传路径和权限

### 学生使用流程
1. 进入缓考申请页面
2. 从下拉列表选择自己的姓名（学号自动填充）
3. 填写其他申请信息
4. 上传相关证明材料
5. 提交申请

现在后端接口已经完全开发完成并编译通过，接下来需要完成前端页面的修改和JavaScript功能的开发。
