# 缓考申请系统开发文档

## 系统概述

按照需求，将缓考申请和缓考审核分离成两个独立的页面，实现职责分离：
- **缓考申请页面**：学生使用，只能申请和查看自己的申请
- **缓考审核页面**：教师/管理员使用，只能审核和查看所有申请

## 系统架构

### 1. 后端架构

#### Controller层
- `SysDeferApplicationController`: 缓考申请控制器
  - 路径：`/system/defer`
  - 功能：学生申请、查看、修改、删除自己的缓考申请
  
- `SysDeferAuditController`: 缓考审核控制器
  - 路径：`/system/deferAudit`
  - 功能：教师审核、查看所有缓考申请

#### Service层
- 复用现有的 `ISysAuditRequestService` 和 `SysAuditRequestServiceImpl`
- 通过 `requestType='defer'` 过滤缓考类型的申请

#### 数据库层
- 复用现有的 `sys_audit_request` 表
- 通过 `request_type` 字段区分不同类型的申请

### 2. 前端架构

#### 缓考申请模块 (`/system/defer/`)
- `defer.html`: 申请列表页面
- `add.html`: 新增申请页面
- `edit.html`: 修改申请页面
- `detail.html`: 申请详情页面

#### 缓考审核模块 (`/system/deferAudit/`)
- `deferAudit.html`: 审核列表页面
- `audit.html`: 审核操作页面
- `detail.html`: 申请详情页面

## 功能特性

### 缓考申请功能

#### ✅ 学生权限控制
- 只能查看和操作自己创建的申请
- 只能修改和删除待审核状态的申请
- 已审核的申请只能查看，不能修改

#### ✅ 申请管理
- **新增申请**：填写学生信息、申请科目、申请原因、详细说明
- **修改申请**：只能修改待审核状态的申请
- **删除申请**：只能删除待审核状态的申请
- **查看详情**：查看申请的完整信息和审核结果

#### ✅ 表单验证
- 学生姓名、学号、申请原因、详细说明为必填项
- 详细说明最少10个字符
- 智能提示不同申请原因需要的证明材料

### 缓考审核功能

#### ✅ 教师权限控制
- 可以查看和审核所有缓考申请
- 不能修改申请的基本信息，只能进行审核操作

#### ✅ 审核管理
- **单个审核**：进入审核页面进行详细审核
- **批量审核**：选择多条记录进行批量通过或驳回
- **快速审核**：在列表中直接进行快速审核操作
- **查看详情**：查看申请的完整信息

#### ✅ 审核功能
- 审核结果：通过/驳回
- 审核意见：必填，提供详细的审核理由
- 自动记录审核人和审核时间

## 页面功能详解

### 1. 缓考申请列表页面 (`defer.html`)

#### 功能按钮
- **申请缓考**：新增缓考申请
- **修改申请**：修改待审核的申请
- **删除申请**：删除待审核的申请
- **查看详情**：查看申请详情
- **导出**：导出申请记录

#### 搜索条件
- 学生姓名、学号
- 申请科目、申请状态、申请原因
- 提交时间

#### 列表显示
- 彩色状态标签（待审核/审核通过/审核驳回）
- 申请原因标签（疾病/受伤/紧急事务/其他）
- 智能操作按钮（根据状态显示不同操作）

### 2. 缓考审核列表页面 (`deferAudit.html`)

#### 功能按钮
- **审核申请**：进入审核页面
- **批量通过**：批量审核通过
- **批量驳回**：批量审核驳回
- **查看详情**：查看申请详情
- **导出**：导出审核记录

#### 快速操作
- 列表中直接显示"通过"、"驳回"按钮
- 支持快速审核操作

### 3. 审核页面 (`audit.html`)

#### 显示信息
- 学生信息（只读）
- 申请信息（只读）
- 证明材料（只读）

#### 审核操作
- 选择审核结果（通过/驳回）
- 填写审核意见（必填）
- 自动记录审核人和时间

## 权限配置

### 菜单结构
```
缓考管理
├── 缓考申请 (system:defer:view)
│   ├── 查询 (system:defer:list)
│   ├── 新增 (system:defer:add)
│   ├── 修改 (system:defer:edit)
│   ├── 删除 (system:defer:remove)
│   ├── 导出 (system:defer:export)
│   └── 详情 (system:defer:detail)
└── 缓考审核 (system:deferAudit:view)
    ├── 查询 (system:deferAudit:list)
    ├── 审核 (system:deferAudit:edit)
    ├── 导出 (system:deferAudit:export)
    └── 详情 (system:deferAudit:detail)
```

### 角色权限分配
- **学生角色**：只能访问缓考申请模块
- **教师角色**：只能访问缓考审核模块
- **管理员角色**：可以访问所有模块

## 技术实现

### 1. 权限控制实现

#### Controller层权限控制
```java
// 学生只能查看自己的申请
if (!isAdmin() && !hasRole("teacher")) {
    sysAuditRequest.setCreateBy(getLoginName());
}

// 学生只能修改待审核状态的申请
if (!getLoginName().equals(existingRequest.getCreateBy()) || 
    !"pending".equals(existingRequest.getRequestStatus())) {
    return AjaxResult.error("无权限修改此申请");
}
```

#### 前端权限控制
```javascript
// 根据申请状态显示不同操作按钮
if (row.requestStatus === 'pending') {
    actions.push('<a class="btn btn-success btn-xs">修改</a>');
    actions.push('<a class="btn btn-danger btn-xs">删除</a>');
}
```

### 2. 批量审核实现

#### 后端接口
```java
@PostMapping("/batchAudit")
public AjaxResult batchAudit(String requestIds, String requestStatus, String auditComment) {
    // 批量更新审核状态
    // 自动设置审核人和审核时间
}
```

#### 前端实现
```javascript
function batchApprove() {
    var rows = $.table.selectColumns("requestId");
    // 批量提交审核请求
}
```

## 部署说明

### 1. 数据库初始化
```sql
-- 1. 创建审核申请表
source sql/sys_audit_request_table.sql;

-- 2. 创建菜单权限
source sql/defer_application_menu.sql;
```

### 2. 代码部署
1. 复制Controller文件到对应目录
2. 复制前端页面到templates目录
3. 重启应用服务

### 3. 权限配置
1. 登录系统管理
2. 在角色管理中为学生角色分配缓考申请权限
3. 为教师角色分配缓考审核权限

## 使用流程

### 学生使用流程
1. 登录系统
2. 进入"缓考申请"页面
3. 点击"申请缓考"按钮
4. 填写申请信息并提交
5. 在列表中查看申请状态
6. 如需修改，在待审核状态下可以修改

### 教师使用流程
1. 登录系统
2. 进入"缓考审核"页面
3. 查看待审核的申请列表
4. 选择申请进行审核：
   - 单个审核：点击"审核"按钮
   - 批量审核：选择多条记录，点击"批量通过"或"批量驳回"
   - 快速审核：直接点击列表中的"通过"或"驳回"按钮
5. 填写审核意见并提交

## 扩展功能建议

### 1. 消息通知
- 申请提交后通知相关教师
- 审核完成后通知学生

### 2. 统计报表
- 按时间段统计申请数量
- 按原因分类统计
- 审核通过率统计

### 3. 移动端支持
- 响应式设计优化
- 微信小程序版本

### 4. 工作流引擎
- 支持多级审核流程
- 自定义审核流程

## 测试建议

### 1. 功能测试
- 学生申请流程测试
- 教师审核流程测试
- 权限控制测试

### 2. 性能测试
- 大量数据下的查询性能
- 批量操作性能

### 3. 安全测试
- 权限绕过测试
- SQL注入测试
- XSS攻击测试

## 维护说明

### 1. 日志监控
- 关键操作日志记录
- 异常情况监控

### 2. 数据备份
- 定期备份申请数据
- 审核记录归档

### 3. 系统优化
- 定期清理过期数据
- 索引优化
- 查询性能优化
