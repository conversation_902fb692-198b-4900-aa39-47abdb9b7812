# 学生成绩表 item_id 字段问题解决方案

## 问题描述

在插入学生成绩数据时出现以下错误：
```
Field 'item_id' doesn't have a default value
```

## 错误原因分析

1. **数据库层面**：`sys_student_score` 表的 `item_id` 字段没有设置默认值，且不允许为 NULL
2. **应用层面**：在调用 `insertSysStudentScore` 方法时，传入的 `SysStudentScore` 对象的 `itemId` 属性为 `null`
3. **MyBatis层面**：由于 `itemId` 为 `null`，动态 SQL 没有包含 `item_id` 字段，导致数据库报错

## 解决方案

### 方案1：数据库层面修复（推荐）

执行 `sql/fix_student_score_table.sql` 脚本：

```sql
-- 修改 item_id 字段允许为 NULL
ALTER TABLE sys_student_score MODIFY COLUMN item_id bigint(20) DEFAULT NULL COMMENT '考核项目ID';

-- 创建考核项目表和基础数据
-- 详见 fix_student_score_table.sql 文件
```

### 方案2：MyBatis 层面修复

已修改 `SysStudentScoreMapper.xml`，确保 `item_id` 字段始终被包含在 INSERT 语句中，即使值为 `null`。

### 方案3：Service 层面修复

已修改 `SysStudentScoreServiceImpl.java`，在插入前检查 `itemId` 是否为空：

```java
@Override
public int insertSysStudentScore(SysStudentScore sysStudentScore)
{
    sysStudentScore.setCreateTime(DateUtils.getNowDate());
    
    // 检查 itemId 是否为空
    if (sysStudentScore.getItemId() == null) {
        throw new RuntimeException("考核项目ID不能为空");
    }
    
    return sysStudentScoreMapper.insertSysStudentScore(sysStudentScore);
}
```

## 建议的完整解决步骤

1. **执行数据库脚本**：
   ```bash
   mysql -u username -p database_name < sql/fix_student_score_table.sql
   ```

2. **重启应用**：确保 MyBatis 映射文件的修改生效

3. **测试验证**：
   - 尝试插入一条学生成绩记录
   - 验证 `item_id` 字段可以为 `null` 或有具体值

## 预防措施

1. **前端表单验证**：在学生成绩录入页面添加考核项目选择的必填验证
2. **后端参数验证**：使用 `@Valid` 注解和 Bean Validation 进行参数校验
3. **数据库设计规范**：对于可选字段，应该设置合适的默认值或允许 NULL

## 相关文件修改清单

- ✅ `ruoyi-system/src/main/resources/mapper/system/SysStudentScoreMapper.xml`
- ✅ `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysStudentScoreServiceImpl.java`
- ✅ `sql/fix_student_score_table.sql` (新增)

## 测试建议

1. **单元测试**：为 `SysStudentScoreService` 编写单元测试
2. **集成测试**：测试完整的学生成绩录入流程
3. **边界测试**：测试 `itemId` 为 `null` 的情况

## 注意事项

- 如果选择方案1（数据库修复），需要评估现有数据的影响
- 如果选择方案3（Service层检查），需要确保所有调用方都能正确设置 `itemId`
- 建议同时使用多个方案以提供多层保护
