package com.ruoyi.web.controller.system;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import java.util.*;

/**
 * 成绩统计分析Controller
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@Controller
@RequestMapping("/system/score/statistics")
public class ScoreStatisticsController extends BaseController
{
    private String prefix = "system/score";

    /**
     * 跳转到成绩统计分析页面
     */
    @GetMapping()
    public String statistics()
    {
        return prefix + "/statistics";
    }

    /**
     * 获取统计概览数据
     */
    @GetMapping("/overview")
    @ResponseBody
    public AjaxResult getOverview()
    {
        Map<String, Object> data = new HashMap<>();
        
        // 模拟统计数据
        data.put("totalStudents", 1248);
        data.put("excellentRate", 68.5);
        data.put("passRate", 92.3);
        data.put("avgScore", 85.2);
        
        return AjaxResult.success(data);
    }

    /**
     * 获取成绩分布数据
     */
    @GetMapping("/gradeDistribution")
    @ResponseBody
    public AjaxResult getGradeDistribution()
    {
        List<Map<String, Object>> data = new ArrayList<>();
        
        Map<String, Object> excellent = new HashMap<>();
        excellent.put("name", "优秀");
        excellent.put("value", 854);
        excellent.put("color", "#5cb85c");
        data.add(excellent);
        
        Map<String, Object> good = new HashMap<>();
        good.put("name", "良好");
        good.put("value", 234);
        good.put("color", "#5bc0de");
        data.add(good);
        
        Map<String, Object> pass = new HashMap<>();
        pass.put("name", "及格");
        pass.put("value", 135);
        pass.put("color", "#f0ad4e");
        data.add(pass);
        
        Map<String, Object> fail = new HashMap<>();
        fail.put("name", "不及格");
        fail.put("value", 25);
        fail.put("color", "#d9534f");
        data.add(fail);
        
        return AjaxResult.success(data);
    }

    /**
     * 获取项目成绩对比数据
     */
    @GetMapping("/itemComparison")
    @ResponseBody
    public AjaxResult getItemComparison()
    {
        Map<String, Object> data = new HashMap<>();
        
        List<String> categories = Arrays.asList("100米跑", "仰卧起坐", "肺活量", "立定跳远", "引体向上");
        List<Double> scores = Arrays.asList(82.5, 88.2, 79.8, 85.6, 76.3);
        
        data.put("categories", categories);
        data.put("scores", scores);
        
        return AjaxResult.success(data);
    }

    /**
     * 获取成绩趋势数据
     */
    @GetMapping("/trend")
    @ResponseBody
    public AjaxResult getTrend()
    {
        Map<String, Object> data = new HashMap<>();
        
        List<String> months = Arrays.asList("2023-09", "2023-10", "2023-11", "2023-12", "2024-01", "2024-02", "2024-03");
        List<Double> excellentRates = Arrays.asList(65.2, 67.8, 69.1, 71.5, 68.9, 70.2, 68.5);
        List<Double> passRates = Arrays.asList(89.5, 91.2, 92.8, 94.1, 91.7, 93.2, 92.3);
        List<Double> avgScores = Arrays.asList(82.1, 83.5, 84.2, 86.1, 83.8, 84.9, 85.2);
        
        data.put("months", months);
        data.put("excellentRates", excellentRates);
        data.put("passRates", passRates);
        data.put("avgScores", avgScores);
        
        return AjaxResult.success(data);
    }

    /**
     * 获取班级排名数据
     */
    @GetMapping("/classRanking")
    @ResponseBody
    public AjaxResult getClassRanking()
    {
        List<Map<String, Object>> data = new ArrayList<>();
        
        String[] classNames = {
            "计算机2021-1班", "软件工程2021-2班", "信息安全2021-1班", "计算机2021-2班", "软件工程2021-1班",
            "网络工程2021-1班", "数据科学2021-1班", "人工智能2021-1班", "物联网2021-1班", "电子商务2021-1班"
        };
        
        Double[] avgScores = {89.5, 88.2, 87.8, 86.9, 86.1, 85.7, 84.9, 84.2, 83.6, 82.8};
        
        for (int i = 0; i < classNames.length; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("rank", i + 1);
            item.put("className", classNames[i]);
            item.put("avgScore", avgScores[i]);
            data.add(item);
        }
        
        return AjaxResult.success(data);
    }

    /**
     * 根据条件获取统计数据
     */
    @GetMapping("/query")
    @ResponseBody
    public AjaxResult queryStatistics(String semester, String grade, String item)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 模拟根据条件查询的数据
        Random random = new Random();
        
        // 概览数据
        Map<String, Object> overview = new HashMap<>();
        overview.put("totalStudents", random.nextInt(500) + 1000);
        overview.put("excellentRate", Math.round((random.nextDouble() * 20 + 60) * 10.0) / 10.0);
        overview.put("passRate", Math.round((random.nextDouble() * 10 + 85) * 10.0) / 10.0);
        overview.put("avgScore", Math.round((random.nextDouble() * 15 + 75) * 10.0) / 10.0);
        
        // 成绩分布数据
        List<Map<String, Object>> gradeDistribution = new ArrayList<>();
        Map<String, Object> excellent = new HashMap<>();
        excellent.put("name", "优秀");
        excellent.put("value", random.nextInt(200) + 700);
        excellent.put("color", "#5cb85c");
        gradeDistribution.add(excellent);
        
        Map<String, Object> good = new HashMap<>();
        good.put("name", "良好");
        good.put("value", random.nextInt(100) + 200);
        good.put("color", "#5bc0de");
        gradeDistribution.add(good);
        
        Map<String, Object> pass = new HashMap<>();
        pass.put("name", "及格");
        pass.put("value", random.nextInt(50) + 100);
        pass.put("color", "#f0ad4e");
        gradeDistribution.add(pass);
        
        Map<String, Object> fail = new HashMap<>();
        fail.put("name", "不及格");
        fail.put("value", random.nextInt(30) + 10);
        fail.put("color", "#d9534f");
        gradeDistribution.add(fail);
        
        // 项目对比数据
        Map<String, Object> itemComparison = new HashMap<>();
        List<String> categories = Arrays.asList("100米跑", "仰卧起坐", "肺活量", "立定跳远", "引体向上");
        List<Double> scores = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            scores.add(Math.round((random.nextDouble() * 20 + 70) * 10.0) / 10.0);
        }
        itemComparison.put("categories", categories);
        itemComparison.put("scores", scores);
        
        result.put("overview", overview);
        result.put("gradeDistribution", gradeDistribution);
        result.put("itemComparison", itemComparison);
        
        return AjaxResult.success(result);
    }
}
