package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.system.domain.SysReservationRecord;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 学生预约Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/studentReservation")
public class SysStudentReservationController extends BaseController
{
    private String prefix = "system/studentReservation";

    @Autowired
    private ISysReservationService sysReservationService;
    
    @Autowired
    private ISysReservationRecordService sysReservationRecordService;

    /**
     * 学生预约页面
     */
    @RequiresPermissions("system:studentReservation:view")
    @GetMapping()
    public String studentReservation()
    {
        return prefix + "/studentReservation";
    }

    /**
     * 查询可预约列表（复用管理端接口）
     */
    @RequiresPermissions("system:studentReservation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation sysReservation)
    {
        startPage();
        // 只查询开放的预约
        sysReservation.setIsOpen("1");
        sysReservation.setStatus("0"); // 正常状态
        List<SysReservation> list = sysReservationService.selectSysReservationList(sysReservation);
        return getDataTable(list);
    }

    /**
     * 预约详情页面
     */
    @RequiresPermissions("system:studentReservation:detail")
    @GetMapping("/detail/{reservationId}")
    public String detail(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("sysReservation", sysReservation);
        return prefix + "/detail";
    }

    /**
     * 预约页面
     */
    @RequiresPermissions("system:studentReservation:reserve")
    @GetMapping("/reserve/{reservationId}")
    public String reserve(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("sysReservation", sysReservation);
        return prefix + "/reserve";
    }

    /**
     * 提交预约
     */
    @RequiresPermissions("system:studentReservation:reserve")
    @Log(title = "学生预约", businessType = BusinessType.INSERT)
    @PostMapping("/reserve")
    @ResponseBody
    public AjaxResult reserveSave(SysReservationRecord sysReservationRecord)
    {
        // 设置预约用户信息
        sysReservationRecord.setUserId(getUserId());
        sysReservationRecord.setUserName(getSysUser().getUserName());
        sysReservationRecord.setStudentId(getSysUser().getLoginName());

        // 设置预约状态
        sysReservationRecord.setRecordStatus("0"); // 正常状态
        sysReservationRecord.setCreateBy(getLoginName());
        sysReservationRecord.setCreateTime(DateUtils.getNowDate());
        sysReservationRecord.setReservationTime(DateUtils.getNowDate());
        
        // 检查是否已经预约过
        SysReservationRecord checkRecord = new SysReservationRecord();
        checkRecord.setReservationId(sysReservationRecord.getReservationId());
        checkRecord.setUserId(getUserId());
        List<SysReservationRecord> existingRecords = sysReservationRecordService.selectSysReservationRecordList(checkRecord);
        
        if (!existingRecords.isEmpty()) {
            return AjaxResult.error("您已经预约过该时段，请勿重复预约");
        }
        
        // 检查名额是否充足
        SysReservation reservation = sysReservationService.selectSysReservationByReservationId(sysReservationRecord.getReservationId());
        if (reservation == null) {
            return AjaxResult.error("预约信息不存在");
        }
        
        // 根据性别检查名额
        String gender = sysReservationRecord.getGender();
        if ("0".equals(gender)) { // 0表示男
            if (reservation.getMaleReserved() >= reservation.getMaleQuota()) {
                return AjaxResult.error("男生名额已满，无法预约");
            }
        } else if ("1".equals(gender)) { // 1表示女
            if (reservation.getFemaleReserved() >= reservation.getFemaleQuota()) {
                return AjaxResult.error("女生名额已满，无法预约");
            }
        }
        
        // 检查总名额
        if (reservation.getTotalReserved() >= reservation.getTotalQuota()) {
            return AjaxResult.error("总名额已满，无法预约");
        }
        
        // 保存预约记录
        int result = sysReservationRecordService.insertSysReservationRecord(sysReservationRecord);
        
        if (result > 0) {
            // 更新预约统计
            updateReservationCount(reservation, gender);
            return AjaxResult.success("预约成功");
        } else {
            return AjaxResult.error("预约失败");
        }
    }

    /**
     * 我的预约列表
     */
    @RequiresPermissions("system:studentReservation:myList")
    @PostMapping("/myList")
    @ResponseBody
    public TableDataInfo myList(SysReservationRecord sysReservationRecord)
    {
        startPage();
        // 只查询当前用户的预约
        sysReservationRecord.setUserId(getUserId());
        List<SysReservationRecord> list = sysReservationRecordService.selectSysReservationRecordList(sysReservationRecord);
        return getDataTable(list);
    }

    /**
     * 取消预约
     */
    @RequiresPermissions("system:studentReservation:cancel")
    @Log(title = "取消预约", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{recordId}")
    @ResponseBody
    public AjaxResult cancel(@PathVariable("recordId") Long recordId)
    {
        SysReservationRecord record = sysReservationRecordService.selectSysReservationRecordByRecordId(recordId);
        if (record == null) {
            return AjaxResult.error("预约记录不存在");
        }
        
        // 检查是否是当前用户的预约
        if (!record.getUserId().equals(getUserId())) {
            return AjaxResult.error("只能取消自己的预约");
        }
        
        // 检查是否可以取消（比如开始前1小时才能取消）
        // 这里可以根据业务需求添加时间限制
        
        // 更新预约状态为已取消
        record.setRecordStatus("1"); // 1表示取消
        record.setUpdateBy(getLoginName());
        record.setUpdateTime(DateUtils.getNowDate());
        
        int result = sysReservationRecordService.updateSysReservationRecord(record);
        
        if (result > 0) {
            // 更新预约统计（减少计数）
            SysReservation reservation = sysReservationService.selectSysReservationByReservationId(record.getReservationId());
            updateReservationCount(reservation, record.getGender(), -1);
            return AjaxResult.success("取消预约成功");
        } else {
            return AjaxResult.error("取消预约失败");
        }
    }

    /**
     * 更新预约统计
     */
    private void updateReservationCount(SysReservation reservation, String gender) {
        updateReservationCount(reservation, gender, 1);
    }
    
    /**
     * 更新预约统计
     */
    private void updateReservationCount(SysReservation reservation, String gender, int increment) {
        if ("男".equals(gender) || "M".equals(gender)) {
            reservation.setMaleReserved(reservation.getMaleReserved() + increment);
        } else if ("女".equals(gender) || "F".equals(gender)) {
            reservation.setFemaleReserved(reservation.getFemaleReserved() + increment);
        }
        reservation.setTotalReserved(reservation.getTotalReserved() + increment);
        sysReservationService.updateSysReservation(reservation);
    }
}
