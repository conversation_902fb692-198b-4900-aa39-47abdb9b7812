package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysAuditRequest;
import com.ruoyi.system.service.ISysAuditRequestService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;

/**
 * 缓考审核Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Controller
@RequestMapping("/system/deferAudit")
public class SysDeferAuditController extends BaseController
{
    private String prefix = "system/deferAudit";

    @Autowired
    private ISysAuditRequestService sysAuditRequestService;

    /**
     * 缓考审核页面
     */
    @RequiresPermissions("system:deferAudit:view")
    @GetMapping()
    public String deferAudit()
    {
        return prefix + "/deferAudit";
    }

    /**
     * 查询缓考审核列表
     */
    @RequiresPermissions("system:deferAudit:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysAuditRequest sysAuditRequest)
    {
        startPage();
        // 只查询缓考类型的申请
        sysAuditRequest.setRequestType("defer");
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        return getDataTable(list);
    }

    /**
     * 导出缓考审核列表
     */
    @RequiresPermissions("system:deferAudit:export")
    @Log(title = "缓考审核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysAuditRequest sysAuditRequest)
    {
        sysAuditRequest.setRequestType("defer");
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        ExcelUtil<SysAuditRequest> util = new ExcelUtil<SysAuditRequest>(SysAuditRequest.class);
        return util.exportExcel(list, "缓考审核数据");
    }

    /**
     * 审核缓考申请
     */
    @RequiresPermissions("system:deferAudit:edit")
    @GetMapping("/audit/{requestId}")
    public String audit(@PathVariable("requestId") Long requestId, ModelMap mmap)
    {
        SysAuditRequest sysAuditRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(requestId);
        mmap.put("sysAuditRequest", sysAuditRequest);
        return prefix + "/audit";
    }

    /**
     * 审核保存缓考申请
     */
    @RequiresPermissions("system:deferAudit:edit")
    @Log(title = "缓考审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    @ResponseBody
    public AjaxResult auditSave(SysAuditRequest sysAuditRequest)
    {
        // 设置审核信息
        sysAuditRequest.setAuditUserName(getSysUser().getUserName());
        sysAuditRequest.setAuditTime(DateUtils.getNowDate());
        sysAuditRequest.setUpdateBy(getLoginName());
        sysAuditRequest.setUpdateTime(DateUtils.getNowDate());
        
        return toAjax(sysAuditRequestService.updateSysAuditRequest(sysAuditRequest));
    }

    /**
     * 批量审核缓考申请
     */
    @RequiresPermissions("system:deferAudit:edit")
    @Log(title = "缓考审核", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAudit")
    @ResponseBody
    public AjaxResult batchAudit(String requestIds, String requestStatus, String auditComment)
    {
        try {
            String[] ids = requestIds.split(",");
            int successCount = 0;
            
            for (String id : ids) {
                SysAuditRequest request = new SysAuditRequest();
                request.setRequestId(Long.parseLong(id.trim()));
                request.setRequestStatus(requestStatus);
                request.setAuditComment(auditComment);
                request.setAuditUserName(getSysUser().getUserName());
                request.setAuditTime(DateUtils.getNowDate());
                request.setUpdateBy(getLoginName());
                request.setUpdateTime(DateUtils.getNowDate());
                
                if (sysAuditRequestService.updateSysAuditRequest(request) > 0) {
                    successCount++;
                }
            }
            
            if (successCount == ids.length) {
                return AjaxResult.success("批量审核成功，共处理 " + successCount + " 条申请");
            } else {
                return AjaxResult.error("批量审核部分失败，成功处理 " + successCount + " 条，共 " + ids.length + " 条申请");
            }
        } catch (Exception e) {
            return AjaxResult.error("批量审核失败：" + e.getMessage());
        }
    }

    /**
     * 查看缓考申请详情
     */
    @RequiresPermissions("system:deferAudit:detail")
    @GetMapping("/detail/{requestId}")
    public String detail(@PathVariable("requestId") Long requestId, ModelMap mmap)
    {
        SysAuditRequest sysAuditRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(requestId);
        mmap.put("sysAuditRequest", sysAuditRequest);
        return prefix + "/detail";
    }
}
