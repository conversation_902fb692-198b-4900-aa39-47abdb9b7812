package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysAuditRequest;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.system.service.ISysAuditRequestService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import org.apache.shiro.SecurityUtils;

/**
 * 缓考申请Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Controller
@RequestMapping("/system/defer")
public class SysDeferApplicationController extends BaseController
{
    private String prefix = "system/defer";

    @Autowired
    private ISysAuditRequestService sysAuditRequestService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    /**
     * 缓考申请页面
     */
    @RequiresPermissions("system:defer:view")
    @GetMapping()
    public String defer()
    {
        return prefix + "/defer";
    }

    /**
     * 查询缓考申请列表
     */
    @RequiresPermissions("system:defer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysAuditRequest sysAuditRequest)
    {
        startPage();
        // 只查询缓考类型的申请
        sysAuditRequest.setRequestType("defer");
        
        // 如果不是管理员且没有审核权限，只能查看自己的申请
        String loginName = getLoginName();
        if (!getSysUser().isAdmin() && !hasPermission("system:deferAudit:view")) {
            sysAuditRequest.setCreateBy(loginName);
        }
        
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        return getDataTable(list);
    }

    /**
     * 导出缓考申请列表
     */
    @RequiresPermissions("system:defer:export")
    @Log(title = "缓考申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysAuditRequest sysAuditRequest)
    {
        sysAuditRequest.setRequestType("defer");
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        ExcelUtil<SysAuditRequest> util = new ExcelUtil<SysAuditRequest>(SysAuditRequest.class);
        return util.exportExcel(list, "缓考申请数据");
    }

    /**
     * 新增缓考申请
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存缓考申请
     */
    @RequiresPermissions("system:defer:add")
    @Log(title = "缓考申请", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysAuditRequest sysAuditRequest)
    {
        // 设置申请类型为缓考
        sysAuditRequest.setRequestType("defer");
        // 设置申请状态为待审核
        sysAuditRequest.setRequestStatus("pending");
        // 设置提交时间
        sysAuditRequest.setSubmitTime(DateUtils.getNowDate());
        // 设置创建者
        sysAuditRequest.setCreateBy(getLoginName());
        sysAuditRequest.setCreateTime(DateUtils.getNowDate());
        
        return toAjax(sysAuditRequestService.insertSysAuditRequest(sysAuditRequest));
    }

    /**
     * 修改缓考申请
     */
    @RequiresPermissions("system:defer:edit")
    @GetMapping("/edit/{requestId}")
    public String edit(@PathVariable("requestId") Long requestId, ModelMap mmap)
    {
        SysAuditRequest sysAuditRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(requestId);
        
        // 检查权限：非管理员且无审核权限的用户只能编辑自己的申请，且状态为待审核
        if (!getSysUser().isAdmin() && !hasPermission("system:deferAudit:view")) {
            if (!getLoginName().equals(sysAuditRequest.getCreateBy()) ||
                !"pending".equals(sysAuditRequest.getRequestStatus())) {
                return "error/unauth";
            }
        }
        
        mmap.put("sysAuditRequest", sysAuditRequest);
        return prefix + "/edit";
    }

    /**
     * 修改保存缓考申请
     */
    @RequiresPermissions("system:defer:edit")
    @Log(title = "缓考申请", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysAuditRequest sysAuditRequest)
    {
        // 非管理员且无审核权限的用户只能修改待审核状态的申请
        if (!getSysUser().isAdmin() && !hasPermission("system:deferAudit:view")) {
            SysAuditRequest existingRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(sysAuditRequest.getRequestId());
            if (!getLoginName().equals(existingRequest.getCreateBy()) ||
                !"pending".equals(existingRequest.getRequestStatus())) {
                return AjaxResult.error("无权限修改此申请");
            }
        }
        
        sysAuditRequest.setUpdateBy(getLoginName());
        sysAuditRequest.setUpdateTime(DateUtils.getNowDate());
        return toAjax(sysAuditRequestService.updateSysAuditRequest(sysAuditRequest));
    }

    /**
     * 删除缓考申请
     */
    @RequiresPermissions("system:defer:remove")
    @Log(title = "缓考申请", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        // 非管理员且无审核权限的用户只能删除自己的待审核申请
        if (!getSysUser().isAdmin() && !hasPermission("system:deferAudit:view")) {
            String[] requestIds = ids.split(",");
            for (String id : requestIds) {
                SysAuditRequest request = sysAuditRequestService.selectSysAuditRequestByRequestId(Long.parseLong(id));
                if (!getLoginName().equals(request.getCreateBy()) ||
                    !"pending".equals(request.getRequestStatus())) {
                    return AjaxResult.error("无权限删除此申请");
                }
            }
        }
        
        return toAjax(sysAuditRequestService.deleteSysAuditRequestByRequestIds(ids));
    }

    /**
     * 查看缓考申请详情
     */
    @RequiresPermissions("system:defer:detail")
    @GetMapping("/detail/{requestId}")
    public String detail(@PathVariable("requestId") Long requestId, ModelMap mmap)
    {
        SysAuditRequest sysAuditRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(requestId);
        mmap.put("sysAuditRequest", sysAuditRequest);
        return prefix + "/detail";
    }

    /**
     * 获取学生用户列表（用于下拉选择）
     */
    @RequiresPermissions("system:defer:list")
    @PostMapping("/studentList")
    @ResponseBody
    public AjaxResult getStudentList()
    {
        try {
            // 查找学生角色
            List<SysRole> allRoles = roleService.selectRoleAll();
            SysRole studentRole = allRoles.stream()
                .filter(role -> "student".equals(role.getRoleKey()) || "学生".equals(role.getRoleName()))
                .findFirst()
                .orElse(null);

            if (studentRole != null) {
                // 使用selectAllocatedList查询已分配学生角色的用户
                SysUser queryUser = new SysUser();
                queryUser.setRoleId(studentRole.getRoleId());
                List<SysUser> studentUsers = userService.selectAllocatedList(queryUser);
                return AjaxResult.success(studentUsers);
            } else {
                // 如果没有找到学生角色，返回所有用户（兼容处理）
                SysUser queryUser = new SysUser();
                List<SysUser> allUsers = userService.selectUserList(queryUser);
                return AjaxResult.success(allUsers);
            }
        } catch (Exception e) {
            return AjaxResult.error("获取学生列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否有指定权限
     */
    private boolean hasPermission(String permission) {
        return SecurityUtils.getSubject().isPermitted(permission);
    }
}
