package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysScoreCriterion;
import com.ruoyi.system.service.ISysScoreCriterionService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 成绩标准Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/criterion")
public class SysScoreCriterionController extends BaseController
{
    private String prefix = "system/criterion";

    @Autowired
    private ISysScoreCriterionService sysScoreCriterionService;

    @GetMapping()
    public String scoreCriterion()
    {
        return prefix + "/criterion";
    }

    /**
     * 查询成绩标准列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysScoreCriterion sysScoreCriterion)
    {
        startPage();
        List<SysScoreCriterion> list = sysScoreCriterionService.selectSysScoreCriterionList(sysScoreCriterion);
        return getDataTable(list);
    }

    /**
     * 导出成绩标准列表
     */
    @Log(title = "成绩标准", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysScoreCriterion sysScoreCriterion)
    {
        List<SysScoreCriterion> list = sysScoreCriterionService.selectSysScoreCriterionList(sysScoreCriterion);
        ExcelUtil<SysScoreCriterion> util = new ExcelUtil<SysScoreCriterion>(SysScoreCriterion.class);
        return util.exportExcel(list, "成绩标准数据");
    }

    /**
     * 新增成绩标准
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存成绩标准
     */
    @Log(title = "成绩标准", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysScoreCriterion sysScoreCriterion)
    {
        return toAjax(sysScoreCriterionService.insertSysScoreCriterion(sysScoreCriterion));
    }

    /**
     * 修改成绩标准
     */
    @GetMapping("/edit/{criterionId}")
    public String edit(@PathVariable("criterionId") Long criterionId, ModelMap mmap)
    {
        SysScoreCriterion sysScoreCriterion = sysScoreCriterionService.selectSysScoreCriterionByCriterionId(criterionId);
        mmap.put("sysScoreCriterion", sysScoreCriterion);
        return prefix + "/edit";
    }

    /**
     * 修改保存成绩标准
     */
    @Log(title = "成绩标准", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysScoreCriterion sysScoreCriterion)
    {
        return toAjax(sysScoreCriterionService.updateSysScoreCriterion(sysScoreCriterion));
    }

    /**
     * 删除成绩标准
     */
    @Log(title = "成绩标准", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysScoreCriterionService.deleteSysScoreCriterionByCriterionIds(ids));
    }
}
