package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysReservationRecord;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预约记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/reservationRecord")
public class SysReservationRecordController extends BaseController
{
    private String prefix = "system/reservationRecord";

    @Autowired
    private ISysReservationRecordService sysReservationRecordService;

    @GetMapping()
    public String reservationRecord()
    {
        return prefix + "/reservationRecord";
    }

    /**
     * 查询预约记录列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservationRecord sysReservationRecord)
    {
        startPage();
        List<SysReservationRecord> list = sysReservationRecordService.selectSysReservationRecordList(sysReservationRecord);
        return getDataTable(list);
    }

    /**
     * 导出预约记录列表
     */
    @Log(title = "预约记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysReservationRecord sysReservationRecord)
    {
        List<SysReservationRecord> list = sysReservationRecordService.selectSysReservationRecordList(sysReservationRecord);
        ExcelUtil<SysReservationRecord> util = new ExcelUtil<SysReservationRecord>(SysReservationRecord.class);
        return util.exportExcel(list, "预约记录数据");
    }

    /**
     * 新增预约记录
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存预约记录
     */
    @Log(title = "预约记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysReservationRecord sysReservationRecord)
    {
        return toAjax(sysReservationRecordService.insertSysReservationRecord(sysReservationRecord));
    }

    /**
     * 修改预约记录
     */
    @GetMapping("/edit/{recordId}")
    public String edit(@PathVariable("recordId") Long recordId, ModelMap mmap)
    {
        SysReservationRecord sysReservationRecord = sysReservationRecordService.selectSysReservationRecordByRecordId(recordId);
        mmap.put("sysReservationRecord", sysReservationRecord);
        return prefix + "/edit";
    }

    /**
     * 修改保存预约记录
     */
    @Log(title = "预约记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysReservationRecord sysReservationRecord)
    {
        return toAjax(sysReservationRecordService.updateSysReservationRecord(sysReservationRecord));
    }

    /**
     * 删除预约记录
     */
    @Log(title = "预约记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysReservationRecordService.deleteSysReservationRecordByRecordIds(ids));
    }
}
