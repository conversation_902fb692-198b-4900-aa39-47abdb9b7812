package com.ruoyi.system.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysStudentScore;
import com.ruoyi.system.service.ISysStudentScoreService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 学生成绩Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/score")
public class SysStudentScoreController extends BaseController
{
    private String prefix = "system/score";

    @Autowired
    private ISysStudentScoreService sysStudentScoreService;

    @RequiresPermissions("system:score:view")
    @GetMapping()
    public String score()
    {
        return prefix + "/score";
    }

    /**
     * 查询学生成绩列表
     */
    @RequiresPermissions("system:score:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysStudentScore sysStudentScore)
    {
        startPage();
        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(sysStudentScore);
        return getDataTable(list);
    }

    /**
     * 导出学生成绩列表
     */
    @RequiresPermissions("system:score:export")
    @Log(title = "学生成绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysStudentScore sysStudentScore)
    {
        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(sysStudentScore);
        ExcelUtil<SysStudentScore> util = new ExcelUtil<SysStudentScore>(SysStudentScore.class);
        return util.exportExcel(list, "学生成绩数据");
    }

    /**
     * 新增学生成绩
     */
    @RequiresPermissions("system:score:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存学生成绩
     */
    @RequiresPermissions("system:score:add")
    @Log(title = "学生成绩", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysStudentScore sysStudentScore)
    {
        return toAjax(sysStudentScoreService.insertSysStudentScore(sysStudentScore));
    }

    /**
     * 修改学生成绩
     */
    @RequiresPermissions("system:score:edit")
    @GetMapping("/edit/{scoreId}")
    public String edit(@PathVariable("scoreId") Long scoreId, ModelMap mmap)
    {
        SysStudentScore sysStudentScore = sysStudentScoreService.selectSysStudentScoreByScoreId(scoreId);
        mmap.put("sysStudentScore", sysStudentScore);
        return prefix + "/edit";
    }

    /**
     * 修改保存学生成绩
     */
    @RequiresPermissions("system:score:edit")
    @Log(title = "学生成绩", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysStudentScore sysStudentScore)
    {
        return toAjax(sysStudentScoreService.updateSysStudentScore(sysStudentScore));
    }

    /**
     * 删除学生成绩
     */
    @RequiresPermissions("system:score:remove")
    @Log(title = "学生成绩", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysStudentScoreService.deleteSysStudentScoreByScoreIds(ids));
    }
}
