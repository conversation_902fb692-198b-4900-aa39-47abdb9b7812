package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysAuditRequest;
import com.ruoyi.system.service.ISysAuditRequestService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 审核申请Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/request")
public class SysAuditRequestController extends BaseController
{
    private String prefix = "system/request";

    @Autowired
    private ISysAuditRequestService sysAuditRequestService;

    @GetMapping()
    public String auditRequest()
    {
        return prefix + "/request";
    }

    /**
     * 查询审核申请列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysAuditRequest sysAuditRequest)
    {
        startPage();
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        return getDataTable(list);
    }

    /**
     * 导出审核申请列表
     */
    @Log(title = "审核申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysAuditRequest sysAuditRequest)
    {
        List<SysAuditRequest> list = sysAuditRequestService.selectSysAuditRequestList(sysAuditRequest);
        ExcelUtil<SysAuditRequest> util = new ExcelUtil<SysAuditRequest>(SysAuditRequest.class);
        return util.exportExcel(list, "审核申请数据");
    }

    /**
     * 新增审核申请
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存审核申请
     */
    @Log(title = "审核申请", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysAuditRequest sysAuditRequest)
    {
        return toAjax(sysAuditRequestService.insertSysAuditRequest(sysAuditRequest));
    }

    /**
     * 修改审核申请
     */
    @GetMapping("/edit/{requestId}")
    public String edit(@PathVariable("requestId") Long requestId, ModelMap mmap)
    {
        SysAuditRequest sysAuditRequest = sysAuditRequestService.selectSysAuditRequestByRequestId(requestId);
        mmap.put("sysAuditRequest", sysAuditRequest);
        return prefix + "/edit";
    }

    /**
     * 修改保存审核申请
     */
    @Log(title = "审核申请", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysAuditRequest sysAuditRequest)
    {
        return toAjax(sysAuditRequestService.updateSysAuditRequest(sysAuditRequest));
    }

    /**
     * 删除审核申请
     */
    @Log(title = "审核申请", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysAuditRequestService.deleteSysAuditRequestByRequestIds(ids));
    }

    /**
     * 批量审核申请
     */
    @Log(title = "审核申请", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAudit")
    @ResponseBody
    public AjaxResult batchAudit(String requestIds, String requestStatus, String auditComment)
    {
        try {
            String[] ids = requestIds.split(",");
            int successCount = 0;

            for (String id : ids) {
                SysAuditRequest request = new SysAuditRequest();
                request.setRequestId(Long.parseLong(id.trim()));
                request.setRequestStatus(requestStatus);
                request.setAuditComment(auditComment);
                request.setAuditUserName(getLoginName());
                request.setAuditTime(new java.util.Date());

                if (sysAuditRequestService.updateSysAuditRequest(request) > 0) {
                    successCount++;
                }
            }

            if (successCount == ids.length) {
                return AjaxResult.success("批量审核成功，共处理 " + successCount + " 条申请");
            } else {
                return AjaxResult.error("批量审核部分失败，成功处理 " + successCount + " 条，共 " + ids.length + " 条申请");
            }
        } catch (Exception e) {
            return AjaxResult.error("批量审核失败：" + e.getMessage());
        }
    }
}
