package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预约管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/reservation")
public class SysReservationController extends BaseController
{
    private String prefix = "system/reservation";

    @Autowired
    private ISysReservationService sysReservationService;

    @GetMapping()
    public String reservation()
    {
        return prefix + "/reservation";
    }

    /**
     * 查询预约管理列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation sysReservation)
    {
        startPage();
        List<SysReservation> list = sysReservationService.selectSysReservationList(sysReservation);
        return getDataTable(list);
    }

    /**
     * 导出预约管理列表
     */
    @Log(title = "预约管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysReservation sysReservation)
    {
        List<SysReservation> list = sysReservationService.selectSysReservationList(sysReservation);
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.exportExcel(list, "预约管理数据");
    }

    /**
     * 新增预约管理
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysReservation sysReservation)
    {
        return toAjax(sysReservationService.insertSysReservation(sysReservation));
    }

    /**
     * 修改预约管理
     */
    @GetMapping("/edit/{reservationId}")
    public String edit(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("sysReservation", sysReservation);
        return prefix + "/edit";
    }

    /**
     * 修改保存预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysReservation sysReservation)
    {
        return toAjax(sysReservationService.updateSysReservation(sysReservation));
    }

    /**
     * 删除预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysReservationService.deleteSysReservationByReservationIds(ids));
    }
}
