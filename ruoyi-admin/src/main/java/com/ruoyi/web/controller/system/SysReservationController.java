package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预约管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/reservation")
public class SysReservationController extends BaseController
{
    private String prefix = "system/reservation";

    @Autowired
    private ISysReservationService sysReservationService;

    @GetMapping()
    public String reservation()
    {
        return prefix + "/reservation";
    }

    /**
     * 查询预约管理列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation sysReservation)
    {
        startPage();
        List<SysReservation> list = sysReservationService.selectSysReservationList(sysReservation);
        return getDataTable(list);
    }

    /**
     * 导出预约管理列表
     */
    @Log(title = "预约管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysReservation sysReservation)
    {
        List<SysReservation> list = sysReservationService.selectSysReservationList(sysReservation);
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.exportExcel(list, "预约管理数据");
    }

    /**
     * 新增预约管理
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysReservation sysReservation)
    {
        // 调试日志：检查接收到的数据
        System.out.println("=== 新增预约数据接收 ===");
        System.out.println("预约名称: " + sysReservation.getReservationName());
        System.out.println("体测日期: " + sysReservation.getTestDate());
        System.out.println("开始时间: " + sysReservation.getStartTime());
        System.out.println("结束时间: " + sysReservation.getEndTime());
        System.out.println("男生名额: " + sysReservation.getMaleQuota());
        System.out.println("女生名额: " + sysReservation.getFemaleQuota());
        System.out.println("预约类型: " + sysReservation.getReservationType());

        // 自动计算总名额
        calculateTotalQuota(sysReservation);

        System.out.println("计算后总名额: " + sysReservation.getTotalQuota());

        int result = sysReservationService.insertSysReservation(sysReservation);
        System.out.println("插入结果: " + result);

        return toAjax(result);
    }

    /**
     * 修改预约管理
     */
    @GetMapping("/edit/{reservationId}")
    public String edit(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("sysReservation", sysReservation);
        return prefix + "/edit";
    }

    /**
     * 修改保存预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysReservation sysReservation)
    {
        // 自动计算总名额
        calculateTotalQuota(sysReservation);
        return toAjax(sysReservationService.updateSysReservation(sysReservation));
    }

    /**
     * 查看预约详情
     */
    @GetMapping("/detail/{reservationId}")
    public String detail(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("sysReservation", sysReservation);
        return prefix + "/detail";
    }

    /**
     * 发布预约
     */
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/open/{reservationId}")
    @ResponseBody
    public AjaxResult open(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        if (sysReservation == null) {
            return AjaxResult.error("预约不存在");
        }
        sysReservation.setIsOpen("1");
        return toAjax(sysReservationService.updateSysReservation(sysReservation));
    }

    /**
     * 取消发布预约
     */
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{reservationId}")
    @ResponseBody
    public AjaxResult close(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation sysReservation = sysReservationService.selectSysReservationByReservationId(reservationId);
        if (sysReservation == null) {
            return AjaxResult.error("预约不存在");
        }
        sysReservation.setIsOpen("0");
        return toAjax(sysReservationService.updateSysReservation(sysReservation));
    }

    /**
     * 克隆预约
     */
    @Log(title = "预约管理", businessType = BusinessType.INSERT)
    @PostMapping("/clone/{reservationId}")
    @ResponseBody
    public AjaxResult clone(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation original = sysReservationService.selectSysReservationByReservationId(reservationId);
        if (original == null) {
            return AjaxResult.error("预约不存在");
        }

        // 创建克隆对象
        SysReservation clone = new SysReservation();
        clone.setReservationCode(original.getReservationCode() + "_copy");
        clone.setReservationName(original.getReservationName() + "_副本");
        clone.setVenueName(original.getVenueName());
        clone.setTestDate(original.getTestDate());
        clone.setStartTime(original.getStartTime());
        clone.setEndTime(original.getEndTime());
        clone.setMaleQuota(original.getMaleQuota());
        clone.setFemaleQuota(original.getFemaleQuota());
        clone.setTotalQuota(original.getTotalQuota());
        clone.setReservationType(original.getReservationType());
        clone.setTestItems(original.getTestItems());
        clone.setRequirements(original.getRequirements());
        clone.setContactPerson(original.getContactPerson());
        clone.setContactPhone(original.getContactPhone());
        clone.setIsOpen("0"); // 默认未发布
        clone.setStatus("0"); // 正常状态
        clone.setMaleReserved(0);
        clone.setFemaleReserved(0);
        clone.setTotalReserved(0);

        return toAjax(sysReservationService.insertSysReservation(clone));
    }

    /**
     * 删除预约管理
     */
    @Log(title = "预约管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysReservationService.deleteSysReservationByReservationIds(ids));
    }

    /**
     * 自动计算总名额和总已预约数
     */
    private void calculateTotalQuota(SysReservation sysReservation)
    {
        // 计算总名额 = 男生名额 + 女生名额
        Integer maleQuota = sysReservation.getMaleQuota() != null ? sysReservation.getMaleQuota() : 0;
        Integer femaleQuota = sysReservation.getFemaleQuota() != null ? sysReservation.getFemaleQuota() : 0;
        sysReservation.setTotalQuota(maleQuota + femaleQuota);

        // 计算总已预约数 = 男生已预约 + 女生已预约
        Integer maleReserved = sysReservation.getMaleReserved() != null ? sysReservation.getMaleReserved() : 0;
        Integer femaleReserved = sysReservation.getFemaleReserved() != null ? sysReservation.getFemaleReserved() : 0;
        sysReservation.setTotalReserved(maleReserved + femaleReserved);
    }
}
