/* 现代化登录页面样式 - 覆盖原有样式 */

/* 重置原有样式 */
body.signin {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    min-height: 100vh !important;
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 主容器 */
.login-container {
    display: flex !important;
    min-height: 100vh !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
}

/* 登录包装器 */
.login-wrapper {
    display: flex !important;
    width: 100% !important;
    max-width: 1000px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 15px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden !important;
    min-height: 600px !important;
}

/* 左侧信息区域 */
.login-left {
    flex: 1 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 60px 40px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    position: relative !important;
}

.login-left::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') !important;
    opacity: 0.3 !important;
}

/* 右侧登录表单 */
.login-right {
    flex: 1 !important;
    padding: 60px 40px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    background: #fff !important;
}

/* 系统标题 */
.system-logo {
    font-size: 28px !important;
    font-weight: bold !important;
    margin-bottom: 20px !important;
    position: relative !important;
    z-index: 1 !important;
    color: white !important;
}

.system-title {
    font-size: 16px !important;
    margin-bottom: 40px !important;
    opacity: 0.9 !important;
    position: relative !important;
    z-index: 1 !important;
    color: white !important;
}

/* 功能列表 */
.feature-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    position: relative !important;
    z-index: 1 !important;
}

.feature-list li {
    padding: 8px 0 !important;
    font-size: 14px !important;
    opacity: 0.8 !important;
    display: flex !important;
    align-items: center !important;
    color: white !important;
}

.feature-list li::before {
    content: '✓' !important;
    margin-right: 10px !important;
    color: #4CAF50 !important;
    font-weight: bold !important;
}

/* 登录表单容器 */
.login-form-container {
    max-width: 350px !important;
    width: 100% !important;
    margin: 0 auto !important;
}

/* 登录标题 */
.login-title {
    font-size: 24px !important;
    font-weight: bold !important;
    color: #333 !important;
    margin-bottom: 30px !important;
    text-align: center !important;
}

/* 表单组 */
.form-group {
    margin-bottom: 20px !important;
}

/* 表单控件 */
.login-right .form-control {
    height: 50px !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 8px !important;
    padding: 0 15px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: #fff !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.login-right .form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* 登录按钮 */
.btn-login {
    width: 100% !important;
    height: 50px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: bold !important;
    transition: all 0.3s ease !important;
    margin-bottom: 20px !important;
    cursor: pointer !important;
}

.btn-login:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* 验证码容器 */
.captcha-container {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

.captcha-input {
    flex: 1 !important;
}

.captcha-image {
    width: 120px !important;
    height: 50px !important;
    border-radius: 8px !important;
    border: 2px solid #e1e5e9 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.captcha-image:hover {
    border-color: #667eea !important;
}

/* 复选框 */
.checkbox-custom {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 20px !important;
}

.checkbox-custom input[type="checkbox"] {
    margin-right: 8px !important;
}

.checkbox-custom label {
    color: #666 !important;
    font-size: 14px !important;
    margin: 0 !important;
    cursor: pointer !important;
}

/* 登录页脚 */
.login-footer {
    text-align: center !important;
    margin-top: 20px !important;
    padding-top: 20px !important;
    border-top: 1px solid #e1e5e9 !important;
    color: #666 !important;
    font-size: 12px !important;
}

/* 版权信息 */
.copyright {
    position: absolute !important;
    bottom: 20px !important;
    left: 40px !important;
    right: 40px !important;
    text-align: center !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 12px !important;
    z-index: 1 !important;
}

/* 隐藏原有的元素 */
.signinpanel {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-wrapper {
        flex-direction: column !important;
        margin: 10px !important;
        min-height: auto !important;
    }
    
    .login-left {
        padding: 40px 30px !important;
        min-height: 200px !important;
    }
    
    .login-right {
        padding: 40px 30px !important;
    }
    
    .copyright {
        position: static !important;
        margin-top: 20px !important;
    }
    
    .system-logo {
        font-size: 24px !important;
    }
    
    .login-form-container {
        max-width: 100% !important;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 10px !important;
    }

    .login-left, .login-right {
        padding: 30px 20px !important;
    }

    .system-logo {
        font-size: 20px !important;
    }

    .login-title {
        font-size: 20px !important;
    }
}

/* 注册页面特定样式 */
.register-title {
    font-size: 24px !important;
    font-weight: bold !important;
    color: #333 !important;
    margin-bottom: 20px !important;
    text-align: center !important;
}

.register-form-container {
    max-width: 400px !important;
    width: 100% !important;
    margin: 0 auto !important;
}

.form-help-text {
    color: #6c757d !important;
    font-size: 12px !important;
    margin-top: 5px !important;
    margin-bottom: 15px !important;
}

.register-form-container .form-group {
    margin-bottom: 15px !important;
}

.register-form-container .form-control {
    height: 45px !important;
}

.terms-checkbox {
    display: flex !important;
    align-items: flex-start !important;
    margin-bottom: 20px !important;
}

.terms-checkbox input[type="checkbox"] {
    margin-right: 8px !important;
    margin-top: 3px !important;
}

.terms-checkbox label {
    color: #666 !important;
    font-size: 13px !important;
    margin: 0 !important;
    line-height: 1.4 !important;
}

.terms-checkbox a {
    color: #667eea !important;
    text-decoration: none !important;
}

.terms-checkbox a:hover {
    text-decoration: underline !important;
}
