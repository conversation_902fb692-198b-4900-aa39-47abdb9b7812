<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>注册 - 大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 学生账号注册">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/css/login-modern.css" th:href="@{/css/login-modern.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        label.error { position:inherit; }

        /* 注册页面特定样式 */
        .register-title {
            font-size: 24px !important;
            font-weight: bold !important;
            color: #333 !important;
            margin-bottom: 20px !important;
            text-align: center !important;
        }

        .register-form-container {
            max-width: 400px !important;
            width: 100% !important;
            margin: 0 auto !important;
        }

        .form-help-text {
            color: #6c757d !important;
            font-size: 12px !important;
            margin-top: 5px !important;
            margin-bottom: 15px !important;
        }

        .register-form-container .form-group {
            margin-bottom: 15px !important;
        }

        .register-form-container .form-control {
            height: 45px !important;
        }

        .terms-checkbox {
            display: flex !important;
            align-items: flex-start !important;
            margin-bottom: 20px !important;
        }

        .terms-checkbox input[type="checkbox"] {
            margin-right: 8px !important;
            margin-top: 3px !important;
        }

        .terms-checkbox label {
            color: #666 !important;
            font-size: 13px !important;
            margin: 0 !important;
            line-height: 1.4 !important;
        }

        .terms-checkbox a {
            color: #667eea !important;
            text-decoration: none !important;
        }

        .terms-checkbox a:hover {
            text-decoration: underline !important;
        }
    </style>
</head>
<body class="signin">
    <div class="login-container">
        <div class="login-wrapper">
            <!-- 左侧信息区域 -->
            <div class="login-left">
                <div class="system-logo">
                    📝 大学生体质测试管理系统
                </div>
                <div class="system-title">
                    欢迎注册 大学生体质测试管理系统
                </div>

                <ul class="feature-list">
                    <li>用户名请使用学号或职员工号</li>
                    <li>密码长度6-20位，建议包含字母数字</li>
                    <li>在校大学生（本科生、研究生）</li>
                    <li>体育教师和管理人员</li>
                    <li>体测数据录入员</li>
                </ul>

                <div class="copyright">
                    Copyright © 2018-2025 ruoyi.vip All Rights Reserved.
                </div>
            </div>

            <!-- 右侧注册表单 -->
            <div class="login-right">
                <div class="register-form-container">
                    <div class="register-title">注册</div>
                    <p style="text-align: center; color: #666; margin-bottom: 25px; font-size: 14px;">
                        请填写完整信息进行注册
                    </p>

                    <form id="registerForm" autocomplete="off">
                        <div class="form-group">
                            <input type="text" name="username" class="form-control" placeholder="学号/工号" maxlength="20" />
                            <div class="form-help-text">请输入您的学号或职员工号</div>
                        </div>

                        <div class="form-group">
                            <input type="email" name="email" class="form-control" placeholder="邮箱地址" maxlength="50" />
                            <div class="form-help-text">用于接收体测通知和成绩报告</div>
                        </div>

                        <div class="form-group">
                            <input type="tel" name="phone" class="form-control" placeholder="手机号码" maxlength="11" />
                            <div class="form-help-text">用于接收重要通知和验证码</div>
                        </div>

                        <div class="form-group">
                            <input type="password" name="password" class="form-control" placeholder="设置密码" maxlength="20" />
                        </div>

                        <div class="form-group">
                            <input type="password" name="confirmPassword" class="form-control" placeholder="确认密码" maxlength="20" />
                        </div>

                        <div class="form-group" th:if="${captchaEnabled==true}">
                            <div class="captcha-container">
                                <input type="text" name="validateCode" class="form-control captcha-input" placeholder="验证码" maxlength="5" />
                                <a href="javascript:void(0);" title="点击更换验证码">
                                    <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode captcha-image"/>
                                </a>
                            </div>
                        </div>

                        <div class="terms-checkbox">
                            <input type="checkbox" id="acceptTerm" name="acceptTerm">
                            <label for="acceptTerm">我已阅读并同意
                                <a href="#">用户协议</a> 和 <a href="#">隐私政策</a>
                            </label>
                        </div>

                        <button class="btn-login" id="btnSubmit" data-loading="正在验证注册，请稍候...">
                            立即注册
                        </button>

                        <div style="text-align: center; margin-top: 20px;">
                            <p style="color: #6c757d; font-size: 14px; margin: 0;">
                                已经有账号？
                                <a th:href="@{/login}" style="color: #667eea; text-decoration: none; font-weight: bold;">
                                    立即登录
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.extend.js" th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/register.js" th:src="@{/ruoyi/register.js}"></script>
</body>
</html>
