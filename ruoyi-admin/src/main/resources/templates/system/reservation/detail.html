<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><i class="fa fa-calendar"></i> 体测预约详情</h5>
                        <div class="ibox-tools">
                            <span class="label" th:classappend="${sysReservation != null and sysReservation.isOpen == '1'} ? 'label-success' : 'label-default'">
                                <span th:text="${sysReservation != null and sysReservation.isOpen == '1'} ? '已发布' : '未发布'"></span>
                            </span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约名称：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${sysReservation?.reservationName ?: '未设置'}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约编码：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${sysReservation?.reservationCode ?: '未设置'}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 时间信息 -->
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测日期：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static text-primary" th:text="${sysReservation?.testDate != null ? #dates.format(sysReservation.testDate, 'yyyy-MM-dd') : '未设置'}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">时间段：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static text-info">
                                            <span th:text="${sysReservation?.startTime != null ? (T(java.lang.String).valueOf(sysReservation.startTime).contains(':') ? T(java.lang.String).valueOf(sysReservation.startTime).substring(0,5) : #dates.format(sysReservation.startTime, 'HH:mm')) : '未设置'}"></span>
                                            -
                                            <span th:text="${sysReservation?.endTime != null ? (T(java.lang.String).valueOf(sysReservation.endTime).contains(':') ? T(java.lang.String).valueOf(sysReservation.endTime).substring(0,5) : #dates.format(sysReservation.endTime, 'HH:mm')) : '未设置'}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 场地信息 -->
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测场地：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${sysReservation?.venueName ?: '未设置'}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约类型：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span class="label label-primary" th:text="${sysReservation?.reservationType ?: '未设置'}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 名额信息 -->
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约名额：</label>
                                    <div class="col-sm-10">
                                        <div class="progress-group">
                                            <span class="progress-text">男生名额</span>
                                            <span class="float-right"><b th:text="${sysReservation?.maleReserved ?: 0}"></b>/<span th:text="${sysReservation?.maleQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-primary" 
                                                     th:style="'width: ' + ${sysReservation != null and sysReservation.maleQuota > 0 ? (sysReservation.maleReserved * 100 / sysReservation.maleQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                        <div class="progress-group">
                                            <span class="progress-text">女生名额</span>
                                            <span class="float-right"><b th:text="${sysReservation?.femaleReserved ?: 0}"></b>/<span th:text="${sysReservation?.femaleQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-danger" 
                                                     th:style="'width: ' + ${sysReservation != null and sysReservation.femaleQuota > 0 ? (sysReservation.femaleReserved * 100 / sysReservation.femaleQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                        <div class="progress-group">
                                            <span class="progress-text">总名额</span>
                                            <span class="float-right"><b th:text="${sysReservation?.totalReserved ?: 0}"></b>/<span th:text="${sysReservation?.totalQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-success" 
                                                     th:style="'width: ' + ${sysReservation != null and sysReservation.totalQuota > 0 ? (sysReservation.totalReserved * 100 / sysReservation.totalQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 测试项目 -->
                        <div class="row" th:if="${!#strings.isEmpty(sysReservation?.testItems)}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">测试项目：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${sysReservation.testItems}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 预约要求 -->
                        <div class="row" th:if="${!#strings.isEmpty(sysReservation?.requirements)}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约要求：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${sysReservation.requirements}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 联系方式 -->
                        <div class="row" th:if="${!#strings.isEmpty(sysReservation?.contactPerson) or !#strings.isEmpty(sysReservation?.contactPhone)}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">联系方式：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">
                                            <i class="fa fa-user"></i> <span th:if="${!#strings.isEmpty(sysReservation?.contactPerson)}" th:text="${sysReservation.contactPerson}"></span>
                                            <i class="fa fa-phone" th:if="${!#strings.isEmpty(sysReservation?.contactPhone)}"></i> <span th:if="${!#strings.isEmpty(sysReservation?.contactPhone)}" th:text="${sysReservation.contactPhone}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 备注信息 -->
                        <div class="row" th:if="${!#strings.isEmpty(sysReservation?.remark)}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">备注信息：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${sysReservation.remark}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="row">
                            <div class="col-sm-12 text-center">
                                <div class="btn-group">
                                    <!-- 管理员操作按钮 -->
                                    <div shiro:hasRole="admin">
                                        <button type="button" class="btn btn-primary" onclick="editReservation()" 
                                                shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-edit"></i> 编辑预约
                                        </button>
                                        <button type="button" class="btn btn-warning" th:if="${sysReservation?.isOpen == '1'}" 
                                                onclick="closeReservation()" shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-pause"></i> 取消发布
                                        </button>
                                        <button type="button" class="btn btn-success" th:if="${sysReservation?.isOpen != '1'}" 
                                                onclick="openReservation()" shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-play"></i> 发布预约
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="cloneReservation()" 
                                                shiro:hasPermission="system:reservation:add">
                                            <i class="fa fa-copy"></i> 克隆预约
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteReservation()" 
                                                shiro:hasPermission="system:reservation:remove">
                                            <i class="fa fa-trash"></i> 删除预约
                                        </button>
                                    </div>
                                    
                                    <button type="button" class="btn btn-default" onclick="$.modal.close()">
                                        <i class="fa fa-close"></i> 关闭
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        var reservationId = /*[[${sysReservation?.reservationId}]]*/ '';

        // 编辑预约
        function editReservation() {
            var url = prefix + "/edit/" + reservationId;
            $.modal.openTab("编辑预约", url);
        }

        // 发布预约
        function openReservation() {
            $.modal.confirm("确认要发布这个预约吗？", function() {
                $.operate.post(prefix + "/open/" + reservationId);
            });
        }

        // 取消发布
        function closeReservation() {
            $.modal.confirm("确认要取消发布这个预约吗？", function() {
                $.operate.post(prefix + "/close/" + reservationId);
            });
        }

        // 克隆预约
        function cloneReservation() {
            $.modal.confirm("确认要克隆这个预约吗？", function() {
                $.operate.post(prefix + "/clone/" + reservationId);
            });
        }

        // 删除预约
        function deleteReservation() {
            $.modal.confirm("确认要删除这个预约吗？删除后无法恢复。", function() {
                $.operate.post(prefix + "/remove", { "ids": reservationId });
            });
        }
    </script>
</body>
</html>
