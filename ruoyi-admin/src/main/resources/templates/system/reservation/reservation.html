<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('体测预约发布管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约名称：</label>
                                <input type="text" name="reservationName" placeholder="请输入预约名称"/>
                            </li>
                            <li>
                                <label>体测日期：</label>
                                <input type="text" class="time-input" placeholder="请选择体测日期" name="testDate"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName" placeholder="请输入场地名称"/>
                            </li>
                            <li>
                                <label>预约类型：</label>
                                <select name="reservationType">
                                    <option value="">所有类型</option>
                                    <option value="FITNESS_TEST">体质测试</option>
                                    <option value="PHYSICAL_EXAM">体检预约</option>
                                    <option value="SPORTS_TEST">运动能力测试</option>
                                    <option value="HEALTH_ASSESSMENT">健康评估</option>
                                    <option value="TRAINING_SESSION">训练课程</option>
                                    <option value="COMPETITION">比赛活动</option>
                                    <option value="MEETING">会议预约</option>
                                    <option value="OTHER">其他</option>
                                </select>
                            </li>
                            <li>
                                <label>发布状态：</label>
                                <select name="isOpen">
                                    <option value="">所有状态</option>
                                    <option value="1">已发布</option>
                                    <option value="0">未发布</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:reservation:add">
                    <i class="fa fa-plus"></i> 发布新预约
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:reservation:edit">
                    <i class="fa fa-edit"></i> 编辑预约
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:reservation:remove">
                    <i class="fa fa-remove"></i> 删除预约
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:reservation:export">
                    <i class="fa fa-download"></i> 导出设置
                </a>
                <a class="btn btn-info" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:reservation:edit')}]] ? '' : 'hidden';
        var removeFlag = [[${@permission.hasPermi('system:reservation:remove')}]] ? '' : 'hidden';
        var addFlag = [[${@permission.hasPermi('system:reservation:add')}]] ? '' : 'hidden';
        var exportFlag = [[${@permission.hasPermi('system:reservation:export')}]] ? '' : 'hidden';
        var prefix = ctx + "system/reservation";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "预约管理",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    // 学生角色默认只查询已发布的预约
                    // 暂时注释掉角色判断，使用权限判断
                    if (userRole === 'student' && !search.isOpen) {
                        search.isOpen = '1'; // 只显示已发布的预约
                    }
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reservationId',
                    title: '预约ID',
                    visible: false
                },
                {
                    field: 'reservationName',
                    title: '预约名称',
                    formatter: function(value, row, index) {
                        return '<strong class="text-primary">' + value + '</strong>';
                    }
                },
                {
                    field: 'testDate',
                    title: '体测日期',
                    formatter: function(value, row, index) {
                        return '<span class="text-info">' + value + '</span>';
                    }
                },
                {
                    field: 'timeRange',
                    title: '时间段',
                    formatter: function(value, row, index) {
                        return '<span class="text-muted">' + row.startTime + ' - ' + row.endTime + '</span>';
                    }
                },
                {
                    field: 'venueName',
                    title: '体测场地'
                },
                {
                    field: 'quotaInfo',
                    title: '预约名额',
                    formatter: function(value, row, index) {
                        var maleInfo = '<span class="text-primary">男: ' + (row.maleReserved || 0) + '/' + (row.maleQuota || 0) + '</span>';
                        var femaleInfo = '<span class="text-danger">女: ' + (row.femaleReserved || 0) + '/' + (row.femaleQuota || 0) + '</span>';
                        var totalInfo = '<span class="text-success">总: ' + (row.totalReserved || 0) + '/' + (row.totalQuota || 0) + '</span>';
                        return maleInfo + ' | ' + femaleInfo + '<br/>' + totalInfo;
                    }
                },
                {
                    field: 'reservationType',
                    title: '预约类型',
                    formatter: function(value, row, index) {
                        var typeMap = {
                            'FITNESS_TEST': '<span class="label label-primary">体质测试</span>',
                            'PHYSICAL_EXAM': '<span class="label label-info">体检预约</span>',
                            'SPORTS_TEST': '<span class="label label-warning">运动能力测试</span>',
                            'HEALTH_ASSESSMENT': '<span class="label label-success">健康评估</span>',
                            'TRAINING_SESSION': '<span class="label label-default">训练课程</span>',
                            'COMPETITION': '<span class="label label-danger">比赛活动</span>',
                            'MEETING': '<span class="label label-inverse">会议预约</span>',
                            'OTHER': '<span class="label label-default">其他</span>'
                        };
                        return typeMap[value] || value;
                    }
                },
                {
                    field: 'isOpen',
                    title: '发布状态',
                    formatter: function(value, row, index) {
                        // 调试信息
                        console.log('isOpen value:', value, 'type:', typeof value);
                        if (value == '1' || value == 1) {
                            return '<span class="badge badge-success">已发布</span>';
                        } else {
                            return '<span class="badge badge-secondary">未发布</span>';
                        }
                    }
                },
                {
                    field: 'contactInfo',
                    title: '联系方式',
                    formatter: function(value, row, index) {
                        return (row.contactPerson || '') + '<br/>' + (row.contactPhone || '');
                    }
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];

                        // 判断是否有管理权限（编辑权限代表管理员）
                        var hasManagePermission = editFlag.indexOf('hidden') === -1;

                        if (hasManagePermission) {
                            // 管理员功能按钮
                            // 编辑按钮
                            actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.reservationId + '\')"><i class="fa fa-edit"></i>编辑</a> ');

                            // 发布/取消发布按钮
                            if (row.isOpen == '1' || row.isOpen == 1) {
                                actions.push('<a class="btn btn-warning btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="closeReservation(\'' + row.reservationId + '\')"><i class="fa fa-pause"></i>取消发布</a> ');
                            } else {
                                actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="openReservation(\'' + row.reservationId + '\')"><i class="fa fa-play"></i>发布预约</a> ');
                            }

                            // 克隆按钮 - 需要新增权限
                            if (addFlag.indexOf('hidden') === -1) {
                                actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="cloneReservation(\'' + row.reservationId + '\')"><i class="fa fa-copy"></i>克隆</a> ');
                            }

                            // 删除按钮 - 需要删除权限
                            if (removeFlag.indexOf('hidden') === -1) {
                                actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.reservationId + '\')"><i class="fa fa-remove"></i>删除</a>');
                            }
                        }

                        // 学生预约按钮（所有用户都可以看到，但只有已发布的才显示）
                        if (row.isOpen == '1' || row.isOpen == 1) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="studentReservation(\'' + row.reservationId + '\')"><i class="fa fa-user-plus"></i>预约</a> ');
                        }

                        // 查看详情按钮（所有人都可以看到）
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewReservationDetail(\'' + row.reservationId + '\')"><i class="fa fa-eye"></i>详情</a>');

                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 发布预约
        function openReservation(reservationId) {
            $.modal.confirm("确认要发布这个预约吗？发布后学生可以看到并进行预约。", function() {
                $.operate.post(prefix + "/open/" + reservationId);
            });
        }

        // 取消发布
        function closeReservation(reservationId) {
            $.modal.confirm("确认要取消发布这个预约吗？取消后学生将无法看到此预约。", function() {
                $.operate.post(prefix + "/close/" + reservationId);
            });
        }

        // 克隆预约
        function cloneReservation(reservationId) {
            $.modal.confirm("确认要克隆这个预约设置吗？将创建一个相同配置的新预约。", function() {
                $.operate.post(prefix + "/clone/" + reservationId);
            });
        }

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 学生预约
        function studentReservation(reservationId) {
            var url = prefix + "/studentReservation/" + reservationId;
            $.modal.openTab("学生预约", url);
        }

        // 查看预约详情
        function viewReservationDetail(reservationId) {
            var url = prefix + "/detail/" + reservationId;
            $.modal.openTab("预约详情", url);
        }
    </script>
</body>
</html>