<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('体测预约发布管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约名称：</label>
                                <input type="text" name="reservationName" placeholder="请输入预约名称"/>
                            </li>
                            <li>
                                <label>体测日期：</label>
                                <input type="text" class="time-input" placeholder="请选择体测日期" name="testDate"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName" placeholder="请输入场地名称"/>
                            </li>
                            <li>
                                <label>预约类型：</label>
                                <select name="reservationType">
                                    <option value="">所有类型</option>
                                    <option value="FITNESS_TEST">体质测试</option>
                                    <option value="PHYSICAL_EXAM">体检预约</option>
                                    <option value="SPORTS_TEST">运动能力测试</option>
                                    <option value="HEALTH_ASSESSMENT">健康评估</option>
                                    <option value="TRAINING_SESSION">训练课程</option>
                                    <option value="COMPETITION">比赛活动</option>
                                    <option value="MEETING">会议预约</option>
                                    <option value="OTHER">其他</option>
                                </select>
                            </li>
                            <li>
                                <label>发布状态：</label>
                                <select name="isOpen">
                                    <option value="">所有状态</option>
                                    <option value="1">已发布</option>
                                    <option value="0">未发布</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()">
                    <i class="fa fa-plus"></i> 发布新预约
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                    <i class="fa fa-edit"></i> 编辑预约
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除预约
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()">
                    <i class="fa fa-download"></i> 导出设置
                </a>
                <a class="btn btn-info" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:reservation:edit')}]] ? '' : 'hidden';
        var removeFlag = [[${@permission.hasPermi('system:reservation:remove')}]] ? '' : 'hidden';
        var addFlag = [[${@permission.hasPermi('system:reservation:add')}]] ? '' : 'hidden';
        var exportFlag = [[${@permission.hasPermi('system:reservation:export')}]] ? '' : 'hidden';
        var prefix = ctx + "system/reservation";

        // 简化权限判断 - 管理员显示所有按钮，学生只显示预约按钮
        var currentUser = [[${session.user}]];
        var isAdmin = true; // 默认当前用户为管理员，显示所有管理功能

        // 如果需要区分学生和管理员，可以根据用户ID或角色判断
        // 这里暂时让所有用户都能看到管理功能

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "预约管理",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    // 去掉角色判断，显示所有预约
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reservationId',
                    title: '预约ID',
                    visible: false
                },
                {
                    field: 'reservationName',
                    title: '预约名称',
                    formatter: function(value, row, index) {
                        return '<strong class="text-primary">' + value + '</strong>';
                    }
                },
                {
                    field: 'testDate',
                    title: '体测日期',
                    formatter: function(value, row, index) {
                        if (value) {
                            var date = new Date(value);
                            if (!isNaN(date.getTime())) {
                                var formatted = date.getFullYear() + '-' +
                                               String(date.getMonth() + 1).padStart(2, '0') + '-' +
                                               String(date.getDate()).padStart(2, '0');
                                return '<span class="text-info">' + formatted + '</span>';
                            }
                        }
                        return '<span class="text-muted">未设置</span>';
                    }
                },
                {
                    field: 'timeRange',
                    title: '时间段',
                    formatter: function(value, row, index) {
                        var startTime = '';
                        var endTime = '';

                        if (row.startTime && row.startTime !== 'null') {
                            if (row.startTime.includes(':')) {
                                startTime = row.startTime.substring(0, 5);
                            } else {
                                var start = new Date(row.startTime);
                                if (!isNaN(start.getTime())) {
                                    startTime = String(start.getHours()).padStart(2, '0') + ':' +
                                               String(start.getMinutes()).padStart(2, '0');
                                }
                            }
                        }

                        if (row.endTime && row.endTime !== 'null') {
                            if (row.endTime.includes(':')) {
                                endTime = row.endTime.substring(0, 5);
                            } else {
                                var end = new Date(row.endTime);
                                if (!isNaN(end.getTime())) {
                                    endTime = String(end.getHours()).padStart(2, '0') + ':' +
                                             String(end.getMinutes()).padStart(2, '0');
                                }
                            }
                        }

                        if (startTime && endTime) {
                            return '<span class="text-muted">' + startTime + ' - ' + endTime + '</span>';
                        } else {
                            return '<span class="text-warning">时间待定</span>';
                        }
                    }
                },
                {
                    field: 'venueName',
                    title: '体测场地'
                },
                {
                    field: 'quotaInfo',
                    title: '预约名额',
                    formatter: function(value, row, index) {
                        var maleInfo = '<span class="text-primary">男: ' + (row.maleReserved || 0) + '/' + (row.maleQuota || 0) + '</span>';
                        var femaleInfo = '<span class="text-danger">女: ' + (row.femaleReserved || 0) + '/' + (row.femaleQuota || 0) + '</span>';
                        var totalInfo = '<span class="text-success">总: ' + (row.totalReserved || 0) + '/' + (row.totalQuota || 0) + '</span>';
                        return maleInfo + ' | ' + femaleInfo + '<br/>' + totalInfo;
                    }
                },
                {
                    field: 'reservationType',
                    title: '预约类型',
                    formatter: function(value, row, index) {
                        var typeMap = {
                            'FITNESS_TEST': '<span class="label label-primary">体质测试</span>',
                            'PHYSICAL_EXAM': '<span class="label label-info">体检预约</span>',
                            'SPORTS_TEST': '<span class="label label-warning">运动能力测试</span>',
                            'HEALTH_ASSESSMENT': '<span class="label label-success">健康评估</span>',
                            'TRAINING_SESSION': '<span class="label label-default">训练课程</span>',
                            'COMPETITION': '<span class="label label-danger">比赛活动</span>',
                            'MEETING': '<span class="label label-inverse">会议预约</span>',
                            'OTHER': '<span class="label label-default">其他</span>'
                        };
                        return typeMap[value] || value;
                    }
                },
                {
                    field: 'isOpen',
                    title: '发布状态',
                    formatter: function(value, row, index) {
                        // 调试信息
                        console.log('isOpen value:', value, 'type:', typeof value);
                        if (value == '1' || value == 1) {
                            return '<span class="badge badge-success">已发布</span>';
                        } else {
                            return '<span class="badge badge-secondary">未发布</span>';
                        }
                    }
                },
                {
                    field: 'contactInfo',
                    title: '联系方式',
                    formatter: function(value, row, index) {
                        return (row.contactPerson || '') + '<br/>' + (row.contactPhone || '');
                    }
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];

                        // 显示所有管理功能按钮（去掉权限限制）
                        // 编辑按钮 - 修复路径
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="editReservation(\'' + row.reservationId + '\')"><i class="fa fa-edit"></i>编辑</a> ');

                        // 发布/取消发布按钮
                        if (row.isOpen == '1' || row.isOpen == 1) {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="closeReservation(\'' + row.reservationId + '\')"><i class="fa fa-pause"></i>取消发布</a> ');
                        } else {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="openReservation(\'' + row.reservationId + '\')"><i class="fa fa-play"></i>发布预约</a> ');
                        }

                        // 克隆按钮
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="cloneReservation(\'' + row.reservationId + '\')"><i class="fa fa-copy"></i>克隆</a> ');

                        // 删除按钮
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="deleteReservation(\'' + row.reservationId + '\')"><i class="fa fa-remove"></i>删除</a>');

                        // 删除学生预约按钮 - 根据您的要求删除
                        // if (row.isOpen == '1' || row.isOpen == 1) {
                        //     actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="studentReservation(\'' + row.reservationId + '\')"><i class="fa fa-user-plus"></i>预约</a> ');
                        // }

                        // 查看详情按钮（所有人都可以看到）
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewReservationDetail(\'' + row.reservationId + '\')"><i class="fa fa-eye"></i>详情</a>');

                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 发布预约
        function openReservation(reservationId) {
            $.modal.confirm("确认要发布这个预约吗？发布后学生可以看到并进行预约。", function() {
                $.operate.post(prefix + "/open/" + reservationId);
            });
        }

        // 取消发布
        function closeReservation(reservationId) {
            $.modal.confirm("确认要取消发布这个预约吗？取消后学生将无法看到此预约。", function() {
                $.operate.post(prefix + "/close/" + reservationId);
            });
        }

        // 克隆预约
        function cloneReservation(reservationId) {
            $.modal.confirm("确认要克隆这个预约设置吗？将创建一个相同配置的新预约。", function() {
                $.operate.post(prefix + "/clone/" + reservationId);
            });
        }

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 编辑预约 - 修复函数
        function editReservation(reservationId) {
            var url = prefix + "/edit/" + reservationId;
            $.modal.openTab("编辑预约", url);
        }

        // 删除预约 - 修复函数
        function deleteReservation(reservationId) {
            $.modal.confirm("确认要删除这个预约吗？删除后无法恢复。", function() {
                $.operate.post(prefix + "/remove", { "ids": reservationId });
            });
        }

        // 查看预约详情 - 修复函数
        function viewReservationDetail(reservationId) {
            var url = prefix + "/detail/" + reservationId;
            $.modal.openTab("预约详情", url);
        }
    </script>
</body>
</html>