<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('编辑体测预约')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-reservation-edit" th:object="${sysReservation}">
            <input name="reservationId" th:field="*{reservationId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">预约编码：</label>
                    <div class="col-sm-8">
                        <input name="reservationCode" th:field="*{reservationCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">预约名称：</label>
                    <div class="col-sm-8">
                        <input name="reservationName" th:field="*{reservationName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">场地ID：</label>
                    <div class="col-sm-8">
                        <input name="venueId" th:field="*{venueId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">场地名称：</label>
                    <div class="col-sm-8">
                        <input name="venueName" th:field="*{venueName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">体测日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="testDate" th:value="${sysReservation.testDate != null ? #dates.format(sysReservation.testDate, 'yyyy-MM-dd') : ''}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">开始时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="startTime" th:value="${sysReservation.startTime != null ? (T(java.lang.String).valueOf(sysReservation.startTime).contains(':') ? T(java.lang.String).valueOf(sysReservation.startTime) : #dates.format(sysReservation.startTime, 'HH:mm:ss')) : ''}" class="form-control" placeholder="HH:mm:ss" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">结束时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="endTime" th:value="${sysReservation.endTime != null ? (T(java.lang.String).valueOf(sysReservation.endTime).contains(':') ? T(java.lang.String).valueOf(sysReservation.endTime) : #dates.format(sysReservation.endTime, 'HH:mm:ss')) : ''}" class="form-control" placeholder="HH:mm:ss" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">男生名额：</label>
                    <div class="col-sm-6">
                        <input name="maleQuota" id="maleQuota" th:field="*{maleQuota}" class="form-control" type="number" min="0" required onchange="calculateTotalQuota()">
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">女生名额：</label>
                    <div class="col-sm-6">
                        <input name="femaleQuota" id="femaleQuota" th:field="*{femaleQuota}" class="form-control" type="number" min="0" required onchange="calculateTotalQuota()">
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label">总名额：</label>
                    <div class="col-sm-6">
                        <input name="totalQuota" id="totalQuota" th:field="*{totalQuota}" class="form-control" type="number" readonly style="background-color: #f5f5f5;">
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 自动计算</span>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约类型：</label>
                    <div class="col-sm-8">
                        <select name="reservationType" th:field="*{reservationType}" class="form-control" required>
                            <option value="">请选择预约类型</option>
                            <option value="FITNESS_TEST">体质测试</option>
                            <option value="PHYSICAL_EXAM">体检预约</option>
                            <option value="SPORTS_TEST">运动能力测试</option>
                            <option value="HEALTH_ASSESSMENT">健康评估</option>
                            <option value="TRAINING_SESSION">训练课程</option>
                            <option value="COMPETITION">比赛活动</option>
                            <option value="MEETING">会议预约</option>
                            <option value="OTHER">其他</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试项目：</label>
                    <div class="col-sm-8">
                        <textarea name="testItems" class="form-control" rows="3" placeholder="例如：身高体重、肺活量、50米跑、立定跳远等">[[*{testItems}]]</textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 详细说明本次体测包含的具体项目</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约要求：</label>
                    <div class="col-sm-8">
                        <textarea name="requirements" class="form-control" rows="3" placeholder="例如：请穿运动服装、运动鞋，携带学生证等">[[*{requirements}]]</textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 学生参加体测需要注意的事项和要求</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">联系人：</label>
                    <div class="col-sm-6">
                        <input name="contactPerson" th:field="*{contactPerson}" class="form-control" type="text" placeholder="负责人姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">联系电话：</label>
                    <div class="col-sm-6">
                        <input name="contactPhone" th:field="*{contactPhone}" class="form-control" type="tel" placeholder="手机号码">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="col-xs-12">
                <div class="form-group">
                    <div class="col-sm-8 col-sm-offset-3">
                        <button type="button" class="btn btn-primary" onclick="submitHandler()">
                            <i class="fa fa-save"></i> 更新预约
                        </button>
                        <button type="button" class="btn btn-default" onclick="$.modal.close()">
                            <i class="fa fa-close"></i> 取消
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        $("#form-reservation-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-reservation-edit').serialize());
            }
        }

        $("input[name='testDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='startTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            minView: 0,
            maxView: 1,
            autoclose: true,
            showMeridian: false
        });

        $("input[name='endTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            minView: 0,
            maxView: 1,
            autoclose: true,
            showMeridian: false
        });

        // 计算总名额函数
        function calculateTotalQuota() {
            var maleQuota = parseInt($('#maleQuota').val()) || 0;
            var femaleQuota = parseInt($('#femaleQuota').val()) || 0;
            var totalQuota = maleQuota + femaleQuota;
            $('#totalQuota').val(totalQuota);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            calculateTotalQuota();
        });
    </script>
</body>
</html>