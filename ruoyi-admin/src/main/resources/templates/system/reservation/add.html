<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('发布新的体测预约')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i>
            <strong>发布体测预约说明：</strong>
            填写完整的预约信息后，可以选择立即发布或稍后发布。发布后，学生将能够看到此预约并进行预约操作。
        </div>

        <form class="form-horizontal m" id="form-reservation-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约编码：</label>
                    <div class="col-sm-8">
                        <input name="reservationCode" class="form-control" type="text" placeholder="系统自动生成或手动输入">
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 用于系统内部识别，可留空自动生成</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">预约名称：</label>
                    <div class="col-sm-8">
                        <input name="reservationName" class="form-control" type="text" placeholder="例如：2024年春季体质测试" required>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 学生看到的预约标题</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">场地ID：</label>
                    <div class="col-sm-8">
                        <input name="venueId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">场地名称：</label>
                    <div class="col-sm-8">
                        <input name="venueName" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">体测日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="testDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">开始时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="startTime" class="form-control" placeholder="HH:mm:ss" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">结束时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="endTime" class="form-control" placeholder="HH:mm:ss" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">男生名额：</label>
                    <div class="col-sm-6">
                        <input name="maleQuota" id="maleQuota" class="form-control" type="number" min="0" placeholder="0" required onchange="calculateTotalQuota()">
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">女生名额：</label>
                    <div class="col-sm-6">
                        <input name="femaleQuota" id="femaleQuota" class="form-control" type="number" min="0" placeholder="0" required onchange="calculateTotalQuota()">
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group">
                    <label class="col-sm-6 control-label">总名额：</label>
                    <div class="col-sm-6">
                        <input name="totalQuota" id="totalQuota" class="form-control" type="number" readonly style="background-color: #f5f5f5;">
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 自动计算</span>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约类型：</label>
                    <div class="col-sm-8">
                        <select name="reservationType" class="form-control" required>
                            <option value="">请选择预约类型</option>
                            <option value="FITNESS_TEST">体质测试</option>
                            <option value="PHYSICAL_EXAM">体检预约</option>
                            <option value="SPORTS_TEST">运动能力测试</option>
                            <option value="HEALTH_ASSESSMENT">健康评估</option>
                            <option value="TRAINING_SESSION">训练课程</option>
                            <option value="COMPETITION">比赛活动</option>
                            <option value="MEETING">会议预约</option>
                            <option value="OTHER">其他</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试项目：</label>
                    <div class="col-sm-8">
                        <textarea name="testItems" class="form-control" rows="3" placeholder="例如：身高体重、肺活量、50米跑、立定跳远等"></textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 详细说明本次体测包含的具体项目</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约要求：</label>
                    <div class="col-sm-8">
                        <textarea name="requirements" class="form-control" rows="3" placeholder="例如：请穿运动服装、运动鞋，携带学生证等"></textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 学生参加体测需要注意的事项和要求</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">联系人：</label>
                    <div class="col-sm-6">
                        <input name="contactPerson" class="form-control" type="text" placeholder="负责人姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">联系电话：</label>
                    <div class="col-sm-6">
                        <input name="contactPhone" class="form-control" type="tel" placeholder="手机号码">
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation"
        $("#form-reservation-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-reservation-add').serialize());
            }
        }

        $("input[name='testDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='startTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            minView: 0,
            maxView: 1,
            autoclose: true,
            showMeridian: false
        });

        $("input[name='endTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            minView: 0,
            maxView: 1,
            autoclose: true,
            showMeridian: false
        });

        // 计算总名额函数
        function calculateTotalQuota() {
            var maleQuota = parseInt($('#maleQuota').val()) || 0;
            var femaleQuota = parseInt($('#femaleQuota').val()) || 0;
            var totalQuota = maleQuota + femaleQuota;
            $('#totalQuota').val(totalQuota);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            calculateTotalQuota();
        });
    </script>
</body>
</html>