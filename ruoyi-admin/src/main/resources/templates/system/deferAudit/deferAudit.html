<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('缓考审核列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学生姓名：</label>
                                <input type="text" name="studentName" placeholder="请输入学生姓名"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentNo" placeholder="请输入学号"/>
                            </li>
                            <li>
                                <label>申请科目：</label>
                                <select name="subjectName">
                                    <option value="">所有科目</option>
                                    <option value="身高体重BMI">身高体重BMI</option>
                                    <option value="肺活量">肺活量</option>
                                    <option value="50米跑">50米跑</option>
                                    <option value="立定跳远">立定跳远</option>
                                    <option value="坐位体前屈">坐位体前屈</option>
                                    <option value="仰卧起坐">仰卧起坐(女)</option>
                                    <option value="引体向上">引体向上(男)</option>
                                    <option value="800米跑">800米跑(女)</option>
                                    <option value="1000米跑">1000米跑(男)</option>
                                    <option value="全部项目">全部项目</option>
                                </select>
                            </li>
                            <li>
                                <label>申请状态：</label>
                                <select name="requestStatus">
                                    <option value="">所有状态</option>
                                    <option value="pending">待审核</option>
                                    <option value="approved">审核通过</option>
                                    <option value="rejected">审核驳回</option>
                                </select>
                            </li>
                            <li>
                                <label>申请原因：</label>
                                <select name="requestReason">
                                    <option value="">所有原因</option>
                                    <option value="illness">疾病</option>
                                    <option value="injury">受伤</option>
                                    <option value="emergency">紧急事务</option>
                                    <option value="other">其他</option>
                                </select>
                            </li>
                            <li>
                                <label>提交时间：</label>
                                <input type="text" class="time-input" placeholder="请选择提交时间" name="submitTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info single disabled" onclick="auditRequest()" shiro:hasPermission="system:deferAudit:edit">
                    <i class="fa fa-edit"></i> 审核申请
                </a>
                <a class="btn btn-success multiple disabled" onclick="batchApprove()" shiro:hasPermission="system:deferAudit:edit">
                    <i class="fa fa-check"></i> 批量通过
                </a>
                <a class="btn btn-warning multiple disabled" onclick="batchReject()" shiro:hasPermission="system:deferAudit:edit">
                    <i class="fa fa-times"></i> 批量驳回
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.detail()" shiro:hasPermission="system:deferAudit:detail">
                    <i class="fa fa-search"></i> 查看详情
                </a>
                <a class="btn btn-default" onclick="$.table.exportExcel()" shiro:hasPermission="system:deferAudit:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:deferAudit:edit')}]];
        var detailFlag = [[${@permission.hasPermi('system:deferAudit:detail')}]];
        var prefix = ctx + "system/deferAudit";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "缓考审核",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'requestId',
                    title: '申请ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名',
                    align: 'center'
                },
                {
                    field: 'studentNo',
                    title: '学号',
                    align: 'center'
                },
                {
                    field: 'subjectName',
                    title: '申请科目',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value || '全部项目';
                    }
                },
                {
                    field: 'requestReason',
                    title: '申请原因',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var reasonNames = {
                            'illness': '<span class="label label-danger">疾病</span>',
                            'injury': '<span class="label label-warning">受伤</span>',
                            'emergency': '<span class="label label-info">紧急事务</span>',
                            'other': '<span class="label label-default">其他</span>'
                        };
                        return reasonNames[value] || value;
                    }
                },
                {
                    field: 'description',
                    title: '详细说明',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'requestStatus',
                    title: '申请状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === 'pending') {
                            return '<span class="label label-warning">待审核</span>';
                        } else if (value === 'approved') {
                            return '<span class="label label-success">审核通过</span>';
                        } else if (value === 'rejected') {
                            return '<span class="label label-danger">审核驳回</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'submitTime',
                    title: '提交时间',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '-';
                    }
                },
                {
                    field: 'auditUserName',
                    title: '审核人',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'auditTime',
                    title: '审核时间',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '-';
                    }
                },
                {
                    field: 'auditComment',
                    title: '审核意见',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'attachmentUrls',
                    title: '附件',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value && value.trim()) {
                            var urls = value.split(',');
                            return '<span class="label label-info">' + urls.length + '个附件</span>';
                        }
                        return '-';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        actions.push('<a class="btn btn-info btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="auditSingle(\'' + row.requestId + '\')"><i class="fa fa-edit"></i>审核</a> ');
                        
                        // 根据状态显示不同的快速审核按钮
                        if (row.requestStatus === 'pending') {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="quickApprove(\'' + row.requestId + '\')"><i class="fa fa-check"></i>通过</a> ');
                            actions.push('<a class="btn btn-warning btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="quickReject(\'' + row.requestId + '\')"><i class="fa fa-times"></i>驳回</a> ');
                        }
                        
                        actions.push('<a class="btn btn-primary btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.requestId + '\')"><i class="fa fa-search"></i>详情</a>');
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 审核申请 */
        function auditRequest() {
            var id = $.table.selectFirstColumns();
            if (id == null) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            auditSingle(id);
        }

        /* 单个审核 */
        function auditSingle(id) {
            var url = prefix + "/audit/" + id;
            $.modal.openTab("审核缓考申请", url);
        }

        /* 查看详情 */
        $.operate.detail = function(id) {
            var url = prefix + "/detail/" + $.operate.getIdByRow(id);
            $.modal.openTab("缓考申请详情", url);
        }

        // 批量审核通过
        function batchApprove() {
            var rows = $.table.selectColumns("requestId");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            
            $.modal.confirm("确认要审核通过选中的 " + rows.length + " 条申请吗？", function() {
                var data = {
                    "requestIds": rows.join(","),
                    "requestStatus": "approved",
                    "auditComment": "批量审核通过"
                };
                $.operate.submit(prefix + "/batchAudit", "post", "json", data);
            });
        }

        // 批量审核驳回
        function batchReject() {
            var rows = $.table.selectColumns("requestId");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            
            $.modal.prompt("请输入驳回理由", function(value) {
                if (!value || value.trim() === '') {
                    $.modal.alertWarning("请输入驳回理由");
                    return false;
                }
                
                var data = {
                    "requestIds": rows.join(","),
                    "requestStatus": "rejected",
                    "auditComment": value
                };
                $.operate.submit(prefix + "/batchAudit", "post", "json", data);
            });
        }

        // 快速审核通过
        function quickApprove(requestId) {
            $.modal.confirm("确认要审核通过这条申请吗？", function() {
                var data = {
                    "requestIds": requestId,
                    "requestStatus": "approved",
                    "auditComment": "审核通过"
                };
                $.operate.submit(prefix + "/batchAudit", "post", "json", data);
            });
        }

        // 快速审核驳回
        function quickReject(requestId) {
            $.modal.prompt("请输入驳回理由", function(value) {
                if (!value || value.trim() === '') {
                    $.modal.alertWarning("请输入驳回理由");
                    return false;
                }

                var data = {
                    "requestIds": requestId,
                    "requestStatus": "rejected",
                    "auditComment": value
                };
                $.operate.submit(prefix + "/batchAudit", "post", "json", data);
            });
        }
    </script>
</body>
</html>
