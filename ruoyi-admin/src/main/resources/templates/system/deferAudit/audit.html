<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('审核缓考申请')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-defer-audit" th:object="${sysAuditRequest}">
            <input name="requestId" th:field="*{requestId}" type="hidden">
            
            <!-- 学生信息区域（只读） -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">学生姓名：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{studentName}"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">学号：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{studentNo}"></p>
                    </div>
                </div>
            </div>
            
            <!-- 缓考申请信息区域（只读） -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">缓考申请信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请科目：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{subjectName} ?: '全部项目'"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请原因：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static">
                            <span th:if="*{requestReason == 'illness'}" class="label label-danger">疾病</span>
                            <span th:if="*{requestReason == 'injury'}" class="label label-warning">受伤</span>
                            <span th:if="*{requestReason == 'emergency'}" class="label label-info">紧急事务</span>
                            <span th:if="*{requestReason == 'other'}" class="label label-default">其他</span>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">详细说明：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm" th:text="*{description}"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">提交时间：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="${#dates.format(sysAuditRequest.submitTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">当前状态：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static">
                            <span th:if="*{requestStatus == 'pending'}" class="label label-warning">待审核</span>
                            <span th:if="*{requestStatus == 'approved'}" class="label label-success">审核通过</span>
                            <span th:if="*{requestStatus == 'rejected'}" class="label label-danger">审核驳回</span>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 附件信息区域（只读） -->
            <div class="form-group" th:if="*{attachmentUrls != null and attachmentUrls != ''}">
                <div class="col-xs-12">
                    <h4 class="form-header h4">证明材料</h4>
                </div>
            </div>
            
            <div class="col-xs-12" th:if="*{attachmentUrls != null and attachmentUrls != ''}">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件链接：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm">
                            <div th:each="url,iterStat : ${#strings.listSplit(sysAuditRequest.attachmentUrls, ',')}">
                                <a th:href="${url}" target="_blank" class="btn btn-link btn-sm">
                                    <i class="fa fa-paperclip"></i> 附件[[${iterStat.count}]]
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 审核信息区域（可编辑） -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">审核信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">审核结果：</label>
                    <div class="col-sm-6">
                        <select name="requestStatus" class="form-control" required>
                            <option value="">请选择审核结果</option>
                            <option value="approved" th:selected="*{requestStatus == 'approved'}">审核通过</option>
                            <option value="rejected" th:selected="*{requestStatus == 'rejected'}">审核驳回</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">审核人：</label>
                    <div class="col-sm-6">
                        <input name="auditUserName" th:value="${@permission.getPrincipalProperty('userName')}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">审核意见：</label>
                    <div class="col-sm-8">
                        <textarea name="auditComment" class="form-control" rows="4" required placeholder="请输入审核意见或驳回理由">[[*{auditComment}]]</textarea>
                        <span class="help-block m-b-none">
                            <i class="fa fa-info-circle"></i> 
                            <strong>审核通过：</strong>请说明通过理由和注意事项<br>
                            <strong>审核驳回：</strong>请详细说明驳回原因和改进建议
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 备注信息 -->
            <div class="col-xs-12" th:if="*{remark != null and remark != ''}">
                <div class="form-group">
                    <label class="col-sm-3 control-label">申请备注：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm" th:text="*{remark}"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/deferAudit";
        
        $("#form-defer-audit").validate({
            focusCleanup: true,
            rules: {
                requestStatus: {
                    required: true
                },
                auditComment: {
                    required: true,
                    minlength: 5
                }
            },
            messages: {
                requestStatus: {
                    required: "请选择审核结果"
                },
                auditComment: {
                    required: "请输入审核意见",
                    minlength: "审核意见至少需要5个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/audit", $('#form-defer-audit').serialize());
            }
        }

        // 审核结果变化时的提示
        $("select[name='requestStatus']").change(function() {
            var status = $(this).val();
            var auditComment = $("textarea[name='auditComment']");
            
            if (status === 'approved') {
                auditComment.attr('placeholder', '请说明审核通过的理由，如：申请理由充分，证明材料齐全，同意缓考申请。');
            } else if (status === 'rejected') {
                auditComment.attr('placeholder', '请详细说明驳回原因，如：证明材料不足、申请理由不充分等，并提供改进建议。');
            } else {
                auditComment.attr('placeholder', '请输入审核意见或驳回理由');
            }
        });

        // 表单分组样式
        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });
    </script>
</body>
</html>
