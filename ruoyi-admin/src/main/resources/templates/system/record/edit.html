<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改预约记录')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-record-edit" th:object="${sysReservationRecord}">
            <input name="recordId" th:field="*{recordId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">用户姓名：</label>
                    <div class="col-sm-8">
                        <input name="userName" th:field="*{userName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">学号：</label>
                    <div class="col-sm-8">
                        <input name="studentId" th:field="*{studentId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">班级名称：</label>
                    <div class="col-sm-8">
                        <input name="className" th:field="*{className}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="reservationTime" th:value="${#dates.format(sysReservationRecord.reservationTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">签到时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="checkInTime" th:value="${#dates.format(sysReservationRecord.checkInTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">签到状态：</label>
                    <div class="col-sm-8">
                        <select name="isCheckedIn" class="form-control">
                            <option value="0" th:selected="${sysReservationRecord.isCheckedIn == '0'}">未签到</option>
                            <option value="1" th:selected="${sysReservationRecord.isCheckedIn == '1'}">已签到</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">记录状态：</label>
                    <div class="col-sm-8">
                        <select name="recordStatus" class="form-control">
                            <option value="pending" th:selected="${sysReservationRecord.recordStatus == 'pending'}">待处理</option>
                            <option value="completed" th:selected="${sysReservationRecord.recordStatus == 'completed'}">已完成</option>
                            <option value="cancelled" th:selected="${sysReservationRecord.recordStatus == 'cancelled'}">已取消</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试结果：</label>
                    <div class="col-sm-8">
                        <textarea name="testResult" th:field="*{testResult}" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12" id="cancelReasonDiv" style="display: none;">
                <div class="form-group">
                    <label class="col-sm-3 control-label">取消原因：</label>
                    <div class="col-sm-8">
                        <input name="cancelReason" th:field="*{cancelReason}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">签到时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="checkInTime" th:value="${#dates.format(sysReservationRecord.checkInTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否签到：</label>
                    <div class="col-sm-8">
                        <input name="isCheckedIn" th:field="*{isCheckedIn}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试结果：</label>
                    <div class="col-sm-8">
                        <textarea name="testResult" class="form-control">[[*{testResult}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">取消原因：</label>
                    <div class="col-sm-8">
                        <input name="cancelReason" th:field="*{cancelReason}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">取消时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="cancelTime" th:value="${#dates.format(sysReservationRecord.cancelTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/record";
        $("#form-record-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-record-edit').serialize());
            }
        }

        // 根据记录状态显示/隐藏取消原因字段
        function toggleCancelReason() {
            var status = $("select[name='recordStatus']").val();
            if (status === 'cancelled') {
                $("#cancelReasonDiv").show();
            } else {
                $("#cancelReasonDiv").hide();
            }
        }

        $(document).ready(function() {
            // 页面加载时检查状态
            toggleCancelReason();

            // 状态改变时切换显示
            $("select[name='recordStatus']").change(function() {
                toggleCancelReason();
            });
        });

        $("input[name='reservationTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='checkInTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='cancelTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>