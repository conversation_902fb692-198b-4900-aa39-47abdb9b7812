<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>用户姓名：</label>
                                <input type="text" name="userName" placeholder="请输入用户姓名"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentId" placeholder="请输入学号"/>
                            </li>
                            <li>
                                <label>班级：</label>
                                <input type="text" name="className" placeholder="请输入班级名称"/>
                            </li>
                            <li>
                                <label>预约时间：</label>
                                <input type="text" class="time-input" placeholder="请选择预约时间" name="reservationTime"/>
                            </li>
                            <li>
                                <label>签到状态：</label>
                                <select name="isCheckedIn" class="form-control">
                                    <option value="">全部</option>
                                    <option value="1">已签到</option>
                                    <option value="0">未签到</option>
                                </select>
                            </li>
                            <li>
                                <label>记录状态：</label>
                                <select name="recordStatus" class="form-control">
                                    <option value="">全部</option>
                                    <option value="completed">已完成</option>
                                    <option value="cancelled">已取消</option>
                                    <option value="pending">待处理</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:record:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:record:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:record:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:record:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:record:remove')}]];
        var prefix = ctx + "system/record";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "预约记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'userName',
                    title: '用户姓名',
                    align: 'center'
                },
                {
                    field: 'studentId',
                    title: '学号',
                    align: 'center'
                },
                {
                    field: 'className',
                    title: '班级',
                    align: 'center'
                },
                {
                    field: 'reservationTime',
                    title: '预约时间',
                    align: 'center'
                },
                {
                    field: 'checkInTime',
                    title: '签到时间',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value ? value : '<span class="text-muted">未签到</span>';
                    }
                },
                {
                    field: 'isCheckedIn',
                    title: '签到状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-success">已签到</span>';
                        } else {
                            return '<span class="badge badge-warning">未签到</span>';
                        }
                    }
                },
                {
                    field: 'testResult',
                    title: '测试结果',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value ? value : '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'recordStatus',
                    title: '记录状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 'completed') {
                            return '<span class="badge badge-success">已完成</span>';
                        } else if (value == 'cancelled') {
                            return '<span class="badge badge-danger">已取消</span>';
                        } else {
                            return '<span class="badge badge-info">待处理</span>';
                        }
                    }
                },
                {
                    field: 'cancelReason',
                    title: '取消原因',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (row.recordStatus == 'cancelled' && value) {
                            return '<span class="text-danger">' + value + '</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.recordId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.recordId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>