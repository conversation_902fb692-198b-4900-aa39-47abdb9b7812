<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('预约详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>预约详情</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="form-horizontal" th:object="${sysReservation}">
                            
                            <!-- 基本信息区域 -->
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">基本信息</h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">预约编码：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{reservationCode}"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">预约名称：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{reservationName}"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">场地名称：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{venueName}"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">预约类型：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{reservationType}"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 时间信息区域 -->
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">时间安排</h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">体测日期：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static" th:text="${sysReservation.testDate != null ? #dates.format(sysReservation.testDate, 'yyyy-MM-dd') : '未设置'}"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">开始时间：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static" th:text="${#dates.format(sysReservation.startTime, 'HH:mm')}"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">结束时间：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static" th:text="${#dates.format(sysReservation.endTime, 'HH:mm')}"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 名额信息区域 -->
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">名额情况</h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-3">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">男生名额：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static">
                                                <span class="text-info" th:text="*{maleReserved}"></span> / 
                                                <span th:text="*{maleQuota}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-3">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">女生名额：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static">
                                                <span class="text-info" th:text="*{femaleReserved}"></span> / 
                                                <span th:text="*{femaleQuota}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-3">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">总名额：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static">
                                                <span class="text-info" th:text="*{totalReserved}"></span> / 
                                                <span th:text="*{totalQuota}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-3">
                                    <div class="form-group">
                                        <label class="col-sm-6 control-label">剩余名额：</label>
                                        <div class="col-sm-6">
                                            <p class="form-control-static">
                                                <span class="text-success" th:text="${sysReservation.totalQuota - sysReservation.totalReserved}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 测试项目区域 -->
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">测试项目</h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">测试项目：</label>
                                        <div class="col-sm-10">
                                            <div class="well well-sm" th:text="*{testItems}"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 预约要求区域 -->
                            <div class="form-group" th:if="${!#strings.isEmpty(sysReservation.requirements)}">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">预约要求</h4>
                                </div>
                            </div>
                            
                            <div class="row" th:if="${!#strings.isEmpty(sysReservation.requirements)}">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">预约要求：</label>
                                        <div class="col-sm-10">
                                            <div class="well well-sm" th:text="*{requirements}"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 联系信息区域 -->
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <h4 class="form-header h4">联系信息</h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">联系人：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{contactPerson}"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">联系电话：</label>
                                        <div class="col-sm-8">
                                            <p class="form-control-static" th:text="*{contactPhone}"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="row">
                                <div class="col-xs-12 text-center">
                                    <button type="button" class="btn btn-success" onclick="makeReservation()" th:if="${sysReservation.totalReserved < sysReservation.totalQuota}">
                                        <i class="fa fa-calendar-plus-o"></i> 立即预约
                                    </button>
                                    <button type="button" class="btn btn-default disabled" th:if="${sysReservation.totalReserved >= sysReservation.totalQuota}">
                                        <i class="fa fa-ban"></i> 名额已满
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="$.modal.close()" style="margin-left: 10px;">
                                        <i class="fa fa-close"></i> 关闭
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var reservationId = /*[[${sysReservation.reservationId}]]*/ '';
        
        $(function() {
            // 表单分组样式
            $(".form-header").css({
                'color': '#333',
                'border-bottom': '2px solid #e7eaec',
                'padding-bottom': '10px',
                'margin-bottom': '20px',
                'margin-top': '20px'
            });
        });
        
        // 立即预约
        function makeReservation() {
            var url = ctx + "system/studentReservation/reserve/" + reservationId;
            $.modal.openTab("预约", url);
        }
    </script>
</body>
</html>
