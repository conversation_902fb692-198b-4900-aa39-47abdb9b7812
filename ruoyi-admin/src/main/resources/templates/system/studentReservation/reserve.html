<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('预约')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-reservation-reserve" th:object="${sysReservation}">
            <input name="reservationId" th:field="*{reservationId}" type="hidden">
            
            <!-- 预约信息展示 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">预约信息</h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">预约名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="*{reservationName}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">场地名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="*{venueName}"></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">体测日期：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static" th:text="${#dates.format(sysReservation.testDate, 'yyyy-MM-dd')}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">开始时间：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static" th:text="${#dates.format(sysReservation.startTime, 'HH:mm')}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">结束时间：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static" th:text="${#dates.format(sysReservation.endTime, 'HH:mm')}"></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 学生信息填写 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">学生姓名：</label>
                        <div class="col-sm-8">
                            <input name="studentName" class="form-control" type="text" required readonly>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">学号：</label>
                        <div class="col-sm-8">
                            <input name="studentNo" class="form-control" type="text" required readonly>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">性别：</label>
                        <div class="col-sm-8">
                            <select name="gender" class="form-control" required>
                                <option value="">请选择性别</option>
                                <option value="0">男</option>
                                <option value="1">女</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">联系电话：</label>
                        <div class="col-sm-8">
                            <input name="phone" class="form-control" type="text" placeholder="请输入联系电话">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">班级：</label>
                        <div class="col-sm-8">
                            <input name="className" class="form-control" type="text" placeholder="请输入班级">
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">院系：</label>
                        <div class="col-sm-8">
                            <input name="deptName" class="form-control" type="text" placeholder="请输入院系">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 特殊说明 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">特殊说明</h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <textarea name="remark" class="form-control" rows="3" placeholder="如有特殊情况或要求，请在此说明"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 名额提醒 -->
            <div class="row">
                <div class="col-xs-12">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>名额提醒：</strong>
                        男生名额：<span class="text-info" th:text="*{maleReserved}"></span>/<span th:text="*{maleQuota}"></span>，
                        女生名额：<span class="text-info" th:text="*{femaleReserved}"></span>/<span th:text="*{femaleQuota}"></span>，
                        总名额：<span class="text-info" th:text="*{totalReserved}"></span>/<span th:text="*{totalQuota}"></span>
                    </div>
                </div>
            </div>
            
            <!-- 预约须知 -->
            <div class="row" th:if="${!#strings.isEmpty(sysReservation.requirements)}">
                <div class="col-xs-12">
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        <strong>预约须知：</strong>
                        <div th:text="*{requirements}"></div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="row">
                <div class="col-xs-12 text-center">
                    <button class="btn btn-success" type="submit">
                        <i class="fa fa-check"></i> 确认预约
                    </button>
                    <button class="btn btn-default" type="button" onclick="$.modal.close()">
                        <i class="fa fa-close"></i> 取消
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/studentReservation";
        
        $(function() {
            // 页面加载时获取当前用户信息
            loadCurrentUser();
            
            // 表单分组样式
            $(".form-header").css({
                'color': '#333',
                'border-bottom': '2px solid #e7eaec',
                'padding-bottom': '10px',
                'margin-bottom': '20px',
                'margin-top': '20px'
            });
        });
        
        // 加载当前用户信息
        function loadCurrentUser() {
            $.post(ctx + "system/defer/currentUser", {}, function(result) {
                if (result.code == 0) {
                    $("input[name='studentName']").val(result.userName);
                    $("input[name='studentNo']").val(result.loginName);
                } else {
                    console.error("获取当前用户信息失败：", result);
                }
            }).fail(function(xhr, status, error) {
                console.error("获取用户信息请求失败：", xhr, status, error);
            });
        }
        
        $("#form-reservation-reserve").validate({
            focusCleanup: true,
            rules: {
                studentName: {
                    required: true,
                    maxlength: 50
                },
                studentNo: {
                    required: true,
                    maxlength: 50
                },
                gender: {
                    required: true
                },
                phone: {
                    maxlength: 20,
                    phone: true
                },
                className: {
                    maxlength: 100
                },
                deptName: {
                    maxlength: 100
                },
                remark: {
                    maxlength: 500
                }
            },
            messages: {
                studentName: {
                    required: "学生姓名不能为空",
                    maxlength: "学生姓名不能超过50个字符"
                },
                studentNo: {
                    required: "学号不能为空",
                    maxlength: "学号不能超过50个字符"
                },
                gender: {
                    required: "请选择性别"
                },
                phone: {
                    maxlength: "联系电话不能超过20个字符",
                    phone: "请输入正确的手机号码"
                },
                className: {
                    maxlength: "班级不能超过100个字符"
                },
                deptName: {
                    maxlength: "院系不能超过100个字符"
                },
                remark: {
                    maxlength: "备注不能超过500个字符"
                }
            },
            submitHandler: function(form) {
                $.operate.save(prefix + "/reserve", $('#form-reservation-reserve').serialize());
            }
        });
        
        // 自定义手机号验证
        $.validator.addMethod("phone", function(value, element) {
            if (value === "") return true; // 非必填字段
            var tel = /^1[3-9]\d{9}$/;
            return tel.test(value);
        }, "请输入正确的手机号码");
    </script>
</body>
</html>
