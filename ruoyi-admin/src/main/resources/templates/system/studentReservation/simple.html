<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>学生预约测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; font-size: 18px; font-weight: bold; }
        .info { color: blue; margin: 10px 0; }
        .test-links { margin: 20px 0; }
        .test-links a { display: block; margin: 5px 0; color: #007bff; text-decoration: none; }
        .test-links a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1 class="success">✅ 学生预约Controller工作正常！</h1>
    
    <div class="info">
        <p><strong>当前时间：</strong><span id="currentTime"></span></p>
        <p><strong>访问路径：</strong>/system/studentReservation</p>
        <p><strong>完整URL：</strong>http://localhost:8080/system/studentReservation</p>
    </div>
    
    <div class="test-links">
        <h3>测试链接：</h3>
        <a href="/system/studentReservation/test" target="_blank">📋 测试接口</a>
        <a href="/system/studentReservation" target="_blank">🏠 学生预约主页面</a>
        <a href="/" target="_blank">🏠 系统首页</a>
    </div>
    
    <div class="info">
        <h3>说明：</h3>
        <ul>
            <li>如果您能看到这个页面，说明Controller路径配置正确</li>
            <li>请确保访问的是 <strong>http://localhost:8080</strong> 而不是 http://localhost</li>
            <li>如果菜单点击没反应，请执行菜单SQL脚本</li>
        </ul>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        
        // 每秒更新时间
        setInterval(function() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }, 1000);
    </script>
</body>
</html>
