<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学生预约管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约名称：</label>
                                <input type="text" name="reservationName" placeholder="请输入预约名称"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName" placeholder="请输入场地名称"/>
                            </li>
                            <li>
                                <label>体测日期：</label>
                                <input type="text" class="time-input" name="testDate" placeholder="请选择体测日期"/>
                            </li>
                            <li>
                                <label>预约类型：</label>
                                <select name="reservationType">
                                    <option value="">所有</option>
                                    <option value="FITNESS_TEST">体质测试</option>
                                    <option value="PHYSICAL_EXAM">体检预约</option>
                                    <option value="SPORTS_TEST">运动能力测试</option>
                                    <option value="HEALTH_ASSESSMENT">健康评估</option>
                                    <option value="TRAINING_SESSION">训练课程</option>
                                    <option value="COMPETITION">比赛活动</option>
                                    <option value="MEETING">会议预约</option>
                                    <option value="OTHER">其他</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info btn-rounded btn-sm" onclick="showMyReservations()">
                    <i class="fa fa-list"></i> 我的预约
                </a>
                <a class="btn btn-success btn-rounded btn-sm" onclick="$.table.refresh()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    
    <!-- 我的预约模态框 -->
    <div class="modal fade" id="myReservationModal" tabindex="-1" role="dialog" aria-labelledby="myReservationModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="myReservationModalLabel">我的预约</h4>
                </div>
                <div class="modal-body">
                    <table id="myReservationTable"></table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // 临时设置为true，跳过权限检查
        var reserveFlag = true; // [[${@permission.hasPermi('system:studentReservation:reserve')}]];
        var detailFlag = true; // [[${@permission.hasPermi('system:studentReservation:detail')}]];
        var myListFlag = true; // [[${@permission.hasPermi('system:studentReservation:myList')}]];
        var cancelFlag = true; // [[${@permission.hasPermi('system:studentReservation:cancel')}]];
        var prefix = ctx + "system/studentReservation";

        $(function() {
            console.log("学生预约页面加载，prefix:", prefix);
            console.log("权限标志:", {reserveFlag, detailFlag, myListFlag, cancelFlag});

            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "学生预约",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reservationId',
                    title: '预约ID',
                    visible: false
                },
                {
                    field: 'reservationCode',
                    title: '预约编码'
                },
                {
                    field: 'reservationName',
                    title: '预约名称'
                },
                {
                    field: 'venueName',
                    title: '场地名称'
                },
                {
                    field: 'testDate',
                    title: '体测日期',
                    formatter: function(value, row, index) {
                        if (value) {
                            // 处理多种日期格式
                            var date = new Date(value);
                            if (!isNaN(date.getTime())) {
                                return date.getFullYear() + '-' +
                                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                                       String(date.getDate()).padStart(2, '0');
                            }
                        }
                        return '';
                    }
                },
                {
                    field: 'timeSlot',
                    title: '时间段',
                    formatter: function(value, row, index) {
                        var startTime = '';
                        var endTime = '';

                        if (row.startTime) {
                            if (row.startTime.includes(':')) {
                                startTime = row.startTime.substring(0, 5); // HH:mm
                            } else {
                                var start = new Date(row.startTime);
                                if (!isNaN(start.getTime())) {
                                    startTime = String(start.getHours()).padStart(2, '0') + ':' +
                                               String(start.getMinutes()).padStart(2, '0');
                                }
                            }
                        }

                        if (row.endTime) {
                            if (row.endTime.includes(':')) {
                                endTime = row.endTime.substring(0, 5); // HH:mm
                            } else {
                                var end = new Date(row.endTime);
                                if (!isNaN(end.getTime())) {
                                    endTime = String(end.getHours()).padStart(2, '0') + ':' +
                                             String(end.getMinutes()).padStart(2, '0');
                                }
                            }
                        }

                        if (startTime && endTime) {
                            return startTime + ' - ' + endTime;
                        } else if (startTime) {
                            return startTime + ' - ?';
                        } else if (endTime) {
                            return '? - ' + endTime;
                        }
                        return '时间待定';
                    }
                },
                {
                    field: 'totalQuota',
                    title: '总名额'
                },
                {
                    field: 'totalReserved',
                    title: '已预约'
                },
                {
                    field: 'availableQuota',
                    title: '剩余名额',
                    formatter: function(value, row, index) {
                        var available = row.totalQuota - row.totalReserved;
                        if (available > 0) {
                            return '<span class="label label-success">' + available + '</span>';
                        } else {
                            return '<span class="label label-danger">已满</span>';
                        }
                    }
                },
                {
                    field: 'reservationType',
                    title: '预约类型',
                    formatter: function(value, row, index) {
                        if (!value) return '未设置';

                        // 预约类型中文映射
                        var typeMap = {
                            'FITNESS_TEST': '体质测试',
                            'PHYSICAL_EXAM': '体检预约',
                            'SPORTS_TEST': '运动能力测试',
                            'HEALTH_ASSESSMENT': '健康评估',
                            'TRAINING_SESSION': '训练课程',
                            'COMPETITION': '比赛活动',
                            'MEETING': '会议预约',
                            'OTHER': '其他'
                        };

                        var label = typeMap[value] || value;
                        var colorClass = '';

                        switch(value) {
                            case 'FITNESS_TEST': colorClass = 'label-primary'; break;
                            case 'PHYSICAL_EXAM': colorClass = 'label-info'; break;
                            case 'SPORTS_TEST': colorClass = 'label-warning'; break;
                            case 'HEALTH_ASSESSMENT': colorClass = 'label-success'; break;
                            case 'TRAINING_SESSION': colorClass = 'label-default'; break;
                            case 'COMPETITION': colorClass = 'label-danger'; break;
                            case 'MEETING': colorClass = 'label-inverse'; break;
                            default: colorClass = 'label-default';
                        }

                        return '<span class="label ' + colorClass + '">' + label + '</span>';
                    }
                },
                {
                    field: 'testItems',
                    title: '测试项目'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        // 详情按钮
                        if (detailFlag) {
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showDetail(\'' + row.reservationId + '\')"><i class="fa fa-search"></i>详情</a> ');
                        }

                        // 预约按钮
                        var available = row.totalQuota - row.totalReserved;
                        if (available > 0 && reserveFlag) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="makeReservation(\'' + row.reservationId + '\')"><i class="fa fa-calendar-plus-o"></i>预约</a>');
                        } else if (available <= 0) {
                            actions.push('<a class="btn btn-default btn-xs disabled"><i class="fa fa-ban"></i>已满</a>');
                        }
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 查看详情 */
        function showDetail(id) {
            console.log("点击详情按钮，ID:", id);
            var url = prefix + "/detail/" + id;
            console.log("详情页面URL:", url);
            $.modal.openTab("预约详情", url);
        }

        /* 预约 */
        function makeReservation(id) {
            console.log("点击预约按钮，ID:", id);
            var url = prefix + "/reserve/" + id;
            console.log("预约页面URL:", url);
            $.modal.openTab("预约", url);
        }

        /* 显示我的预约 */
        function showMyReservations() {
            if (!myListFlag) {
                $.modal.msgError("您没有查看我的预约的权限");
                return;
            }
            
            $('#myReservationModal').modal('show');
            
            // 初始化我的预约表格
            $('#myReservationTable').bootstrapTable('destroy');
            $('#myReservationTable').bootstrapTable({
                url: prefix + "/myList",
                method: 'post',
                striped: true,
                cache: false,
                pagination: true,
                sortable: false,
                sidePagination: "server",
                pageNumber: 1,
                pageSize: 10,
                pageList: [10, 25, 50],
                search: false,
                showColumns: false,
                showRefresh: true,
                showToggle: false,
                columns: [{
                    field: 'reservationName',
                    title: '预约名称'
                },
                {
                    field: 'venueName',
                    title: '场地名称'
                },
                {
                    field: 'testDate',
                    title: '体测日期',
                    formatter: function(value, row, index) {
                        if (value) {
                            var date = new Date(value);
                            if (!isNaN(date.getTime())) {
                                return date.getFullYear() + '-' +
                                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                                       String(date.getDate()).padStart(2, '0');
                            }
                        }
                        return '';
                    }
                },
                {
                    field: 'reservationTime',
                    title: '预约时间',
                    formatter: function(value, row, index) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD HH:mm');
                        }
                        return '';
                    }
                },
                {
                    field: 'recordStatus',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value === '0') {
                            return '<span class="label label-success">正常</span>';
                        } else if (value === '1') {
                            return '<span class="label label-danger">已取消</span>';
                        } else {
                            return '<span class="label label-warning">未知</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.recordStatus === '0') {
                            actions.push('<a class="btn btn-danger btn-xs ' + cancelFlag + '" href="javascript:void(0)" onclick="cancelReservation(\'' + row.recordId + '\')"><i class="fa fa-times"></i>取消</a>');
                        }
                        return actions.join('');
                    }
                }]
            });
        }

        /* 取消预约 */
        function cancelReservation(recordId) {
            $.modal.confirm("确定要取消这个预约吗？", function() {
                $.operate.post(prefix + "/cancel/" + recordId);
                $('#myReservationTable').bootstrapTable('refresh');
                $.table.refresh();
            });
        }

        // 字典数据
        var reservationTypeDatas = [[${@dict.getType('reservation_type')}]];
    </script>
</body>
</html>
