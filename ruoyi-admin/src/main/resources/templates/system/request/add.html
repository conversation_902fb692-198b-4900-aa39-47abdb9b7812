<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增审核申请')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: fileinput-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-request-add">
            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学生姓名：</label>
                    <div class="col-sm-6">
                        <input name="studentName" class="form-control" type="text" required placeholder="请输入学生姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学号：</label>
                    <div class="col-sm-6">
                        <input name="studentNo" class="form-control" type="text" required placeholder="请输入学号">
                    </div>
                </div>
            </div>

            <!-- 申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">申请类型：</label>
                    <div class="col-sm-6">
                        <select name="requestType" class="form-control" required>
                            <option value="">请选择申请类型</option>
                            <option value="defer">缓考</option>
                            <option value="exempt">免考</option>
                            <option value="makeup">补考</option>
                            <option value="retest">重测</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请科目：</label>
                    <div class="col-sm-6">
                        <select name="subjectName" class="form-control">
                            <option value="">请选择测试项目</option>
                            <option value="身高体重BMI">身高体重BMI</option>
                            <option value="肺活量">肺活量</option>
                            <option value="50米跑">50米跑</option>
                            <option value="立定跳远">立定跳远</option>
                            <option value="坐位体前屈">坐位体前屈</option>
                            <option value="仰卧起坐">仰卧起坐(女)</option>
                            <option value="引体向上">引体向上(男)</option>
                            <option value="800米跑">800米跑(女)</option>
                            <option value="1000米跑">1000米跑(男)</option>
                            <option value="全部项目">全部项目</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">申请原因：</label>
                    <div class="col-sm-8">
                        <select name="requestReason" class="form-control" required>
                            <option value="">请选择申请原因</option>
                            <option value="illness">疾病</option>
                            <option value="injury">受伤</option>
                            <option value="emergency">紧急事务</option>
                            <option value="other">其他</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请根据实际情况选择申请原因</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">详细说明：</label>
                    <div class="col-sm-8">
                        <textarea name="description" class="form-control" rows="4" required placeholder="请详细说明申请的具体情况，如病情描述、受伤部位、紧急事务性质等"></textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请提供详细的情况说明，有助于审核</span>
                    </div>
                </div>
            </div>

            <!-- 附件信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">附件信息</h4>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">证明材料：</label>
                    <div class="col-sm-8">
                        <input type="file" id="file-upload" name="files" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                        <span class="help-block m-b-none">
                            <i class="fa fa-info-circle"></i>
                            支持上传医院证明、诊断书、请假条等相关证明材料<br>
                            支持格式：JPG、PNG、PDF、DOC、DOCX，单个文件不超过5MB
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件URL：</label>
                    <div class="col-sm-8">
                        <textarea name="attachmentUrls" class="form-control" rows="2" placeholder="如果有在线附件链接，请在此输入，多个URL用逗号分隔"></textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选项，用于补充在线附件链接</span>
                    </div>
                </div>
            </div>

            <!-- 申请状态区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请状态</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请状态：</label>
                    <div class="col-sm-6">
                        <select name="requestStatus" class="form-control">
                            <option value="pending" selected>待审核</option>
                            <option value="approved">审核通过</option>
                            <option value="rejected">审核驳回</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">提交时间：</label>
                    <div class="col-sm-6">
                        <div class="input-group date">
                            <input name="submitTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text" value="">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 不填写则自动设置为当前时间</span>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: fileinput-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/request";

        $("#form-request-add").validate({
            focusCleanup: true,
            rules: {
                studentName: {
                    required: true,
                    maxlength: 100
                },
                studentNo: {
                    required: true,
                    maxlength: 50
                },
                requestType: {
                    required: true
                },
                requestReason: {
                    required: true
                },
                description: {
                    required: true,
                    minlength: 10
                }
            },
            messages: {
                studentName: {
                    required: "请输入学生姓名",
                    maxlength: "学生姓名不能超过100个字符"
                },
                studentNo: {
                    required: "请输入学号",
                    maxlength: "学号不能超过50个字符"
                },
                requestType: {
                    required: "请选择申请类型"
                },
                requestReason: {
                    required: "请选择申请原因"
                },
                description: {
                    required: "请输入详细说明",
                    minlength: "详细说明至少需要10个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 如果没有设置提交时间，自动设置为当前时间
                if (!$("input[name='submitTime']").val()) {
                    var now = new Date();
                    var timeStr = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
                    $("input[name='submitTime']").val(timeStr);
                }

                $.operate.save(prefix + "/add", $('#form-request-add').serialize());
            }
        }

        // 日期时间选择器
        $("input[name='submitTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            autoclose: true,
            todayBtn: true,
            todayHighlight: true
        });

        // 申请类型变化时的提示
        $("select[name='requestType']").change(function() {
            var type = $(this).val();
            var hints = {
                'defer': '缓考：因特殊情况无法按时参加测试，申请延期测试',
                'exempt': '免考：因身体原因长期无法参加某项测试，申请免除该项目',
                'makeup': '补考：因故错过测试时间，申请补充测试',
                'retest': '重测：对测试结果有异议或测试过程中出现问题，申请重新测试'
            };
            if (type && hints[type]) {
                $.modal.msg(hints[type]);
            }
        });

        // 申请原因变化时的提示
        $("select[name='requestReason']").change(function() {
            var reason = $(this).val();
            var hints = {
                'illness': '疾病：请提供医院诊断证明或病假条',
                'injury': '受伤：请提供医院诊断证明和康复建议',
                'emergency': '紧急事务：请详细说明紧急事务的性质',
                'other': '其他：请在详细说明中具体描述情况'
            };
            if (reason && hints[reason]) {
                $.modal.msg(hints[reason]);
            }
        });

        // 文件上传初始化
        $("#file-upload").fileinput({
            language: 'zh',
            uploadUrl: ctx + "common/upload",
            allowedFileExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
            maxFileSize: 5120, // 5MB
            maxFilesNum: 5,
            showUpload: false,
            showRemove: false,
            showPreview: true,
            browseClass: "btn btn-primary",
            browseLabel: "选择文件",
            browseIcon: "<i class=\"fa fa-folder-open\"></i> ",
            removeClass: "btn btn-danger",
            removeLabel: "删除",
            removeIcon: "<i class=\"fa fa-trash\"></i> "
        });

        // 表单分组样式
        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });
    </script>
</body>
</html>