<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增审核申请')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: fileinput-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-request-add">
            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学生姓名：</label>
                    <div class="col-sm-6">
                        <input name="studentName" class="form-control" type="text" required placeholder="请输入学生姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学号：</label>
                    <div class="col-sm-6">
                        <input name="studentNo" class="form-control" type="text" required placeholder="请输入学号">
                    </div>
                </div>
            </div>

            <!-- 申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">申请类型：</label>
                    <div class="col-sm-6">
                        <select name="requestType" class="form-control" required>
                            <option value="">请选择申请类型</option>
                            <option value="defer">缓考</option>
                            <option value="exempt">免考</option>
                            <option value="makeup">补考</option>
                            <option value="retest">重测</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请科目：</label>
                    <div class="col-sm-6">
                        <select name="subjectName" class="form-control">
                            <option value="">请选择测试项目</option>
                            <option value="身高体重BMI">身高体重BMI</option>
                            <option value="肺活量">肺活量</option>
                            <option value="50米跑">50米跑</option>
                            <option value="立定跳远">立定跳远</option>
                            <option value="坐位体前屈">坐位体前屈</option>
                            <option value="仰卧起坐">仰卧起坐(女)</option>
                            <option value="引体向上">引体向上(男)</option>
                            <option value="800米跑">800米跑(女)</option>
                            <option value="1000米跑">1000米跑(男)</option>
                            <option value="全部项目">全部项目</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">申请原因：</label>
                    <div class="col-sm-8">
                        <select name="requestReason" class="form-control" required>
                            <option value="">请选择申请原因</option>
                            <option value="illness">疾病</option>
                            <option value="injury">受伤</option>
                            <option value="emergency">紧急事务</option>
                            <option value="other">其他</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请根据实际情况选择申请原因</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件URL列表(多个URL用逗号分隔)：</label>
                    <div class="col-sm-8">
                        <textarea name="attachmentUrls" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">提交时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="submitTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核人姓名：</label>
                    <div class="col-sm-8">
                        <input name="auditUserName" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="auditTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核意见/驳回理由：</label>
                    <div class="col-sm-8">
                        <textarea name="auditComment" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/request"
        $("#form-request-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-request-add').serialize());
            }
        }

        $("input[name='submitTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='auditTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>