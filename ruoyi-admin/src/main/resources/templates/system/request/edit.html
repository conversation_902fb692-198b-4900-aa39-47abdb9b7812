<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改审核申请')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-request-edit" th:object="${sysAuditRequest}">
            <input name="requestId" th:field="*{requestId}" type="hidden">

            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学生姓名：</label>
                    <div class="col-sm-6">
                        <input name="studentName" th:field="*{studentName}" class="form-control" type="text" required placeholder="请输入学生姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学号：</label>
                    <div class="col-sm-6">
                        <input name="studentNo" th:field="*{studentNo}" class="form-control" type="text" required placeholder="请输入学号">
                    </div>
                </div>
            </div>

            <!-- 申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">申请类型：</label>
                    <div class="col-sm-6">
                        <select name="requestType" class="form-control" required>
                            <option value="">请选择申请类型</option>
                            <option value="defer" th:selected="*{requestType == 'defer'}">缓考</option>
                            <option value="exempt" th:selected="*{requestType == 'exempt'}">免考</option>
                            <option value="makeup" th:selected="*{requestType == 'makeup'}">补考</option>
                            <option value="retest" th:selected="*{requestType == 'retest'}">重测</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请科目：</label>
                    <div class="col-sm-6">
                        <select name="subjectName" class="form-control">
                            <option value="">请选择测试项目</option>
                            <option value="身高体重BMI" th:selected="*{subjectName == '身高体重BMI'}">身高体重BMI</option>
                            <option value="肺活量" th:selected="*{subjectName == '肺活量'}">肺活量</option>
                            <option value="50米跑" th:selected="*{subjectName == '50米跑'}">50米跑</option>
                            <option value="立定跳远" th:selected="*{subjectName == '立定跳远'}">立定跳远</option>
                            <option value="坐位体前屈" th:selected="*{subjectName == '坐位体前屈'}">坐位体前屈</option>
                            <option value="仰卧起坐" th:selected="*{subjectName == '仰卧起坐'}">仰卧起坐(女)</option>
                            <option value="引体向上" th:selected="*{subjectName == '引体向上'}">引体向上(男)</option>
                            <option value="800米跑" th:selected="*{subjectName == '800米跑'}">800米跑(女)</option>
                            <option value="1000米跑" th:selected="*{subjectName == '1000米跑'}">1000米跑(男)</option>
                            <option value="全部项目" th:selected="*{subjectName == '全部项目'}">全部项目</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">申请原因：</label>
                    <div class="col-sm-8">
                        <select name="requestReason" class="form-control" required>
                            <option value="">请选择申请原因</option>
                            <option value="illness" th:selected="*{requestReason == 'illness'}">疾病</option>
                            <option value="injury" th:selected="*{requestReason == 'injury'}">受伤</option>
                            <option value="emergency" th:selected="*{requestReason == 'emergency'}">紧急事务</option>
                            <option value="other" th:selected="*{requestReason == 'other'}">其他</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请根据实际情况选择申请原因</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">详细说明：</label>
                    <div class="col-sm-8">
                        <textarea name="description" class="form-control" rows="4" required placeholder="请详细说明申请的具体情况">[[*{description}]]</textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请提供详细的情况说明，有助于审核</span>
                    </div>
                </div>
            </div>

            <!-- 附件信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">附件信息</h4>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件URL：</label>
                    <div class="col-sm-8">
                        <textarea name="attachmentUrls" class="form-control" rows="2" placeholder="多个URL用逗号分隔">[[*{attachmentUrls}]]</textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 证明材料的在线链接地址</span>
                    </div>
                </div>
            </div>

            <!-- 申请状态区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请状态</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请状态：</label>
                    <div class="col-sm-6">
                        <select name="requestStatus" class="form-control">
                            <option value="pending" th:selected="*{requestStatus == 'pending'}">待审核</option>
                            <option value="approved" th:selected="*{requestStatus == 'approved'}">审核通过</option>
                            <option value="rejected" th:selected="*{requestStatus == 'rejected'}">审核驳回</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">提交时间：</label>
                    <div class="col-sm-6">
                        <div class="input-group date">
                            <input name="submitTime" th:value="${#dates.format(sysAuditRequest.submitTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">审核信息</h4>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">审核人：</label>
                    <div class="col-sm-6">
                        <input name="auditUserName" th:field="*{auditUserName}" class="form-control" type="text" placeholder="审核人姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">审核时间：</label>
                    <div class="col-sm-6">
                        <div class="input-group date">
                            <input name="auditTime" th:value="${#dates.format(sysAuditRequest.auditTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核意见：</label>
                    <div class="col-sm-8">
                        <textarea name="auditComment" class="form-control" rows="3" placeholder="请输入审核意见或驳回理由">[[*{auditComment}]]</textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 审核通过或驳回的具体理由</span>
                    </div>
                </div>
            </div>

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/request";

        $("#form-request-edit").validate({
            focusCleanup: true,
            rules: {
                studentName: {
                    required: true,
                    maxlength: 100
                },
                studentNo: {
                    required: true,
                    maxlength: 50
                },
                requestType: {
                    required: true
                },
                requestReason: {
                    required: true
                },
                description: {
                    required: true,
                    minlength: 10
                }
            },
            messages: {
                studentName: {
                    required: "请输入学生姓名",
                    maxlength: "学生姓名不能超过100个字符"
                },
                studentNo: {
                    required: "请输入学号",
                    maxlength: "学号不能超过50个字符"
                },
                requestType: {
                    required: "请选择申请类型"
                },
                requestReason: {
                    required: "请选择申请原因"
                },
                description: {
                    required: "请输入详细说明",
                    minlength: "详细说明至少需要10个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 如果审核状态改为通过或驳回，但没有填写审核时间，自动设置为当前时间
                var status = $("select[name='requestStatus']").val();
                if ((status === 'approved' || status === 'rejected') && !$("input[name='auditTime']").val()) {
                    var now = new Date();
                    var timeStr = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
                    $("input[name='auditTime']").val(timeStr);
                }

                $.operate.save(prefix + "/edit", $('#form-request-edit').serialize());
            }
        }

        // 日期时间选择器
        $("input[name='submitTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            autoclose: true,
            todayBtn: true,
            todayHighlight: true
        });

        $("input[name='auditTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            autoclose: true,
            todayBtn: true,
            todayHighlight: true
        });

        // 申请状态变化时的处理
        $("select[name='requestStatus']").change(function() {
            var status = $(this).val();
            var auditUserName = $("input[name='auditUserName']");
            var auditComment = $("textarea[name='auditComment']");

            if (status === 'approved' || status === 'rejected') {
                // 如果没有审核人，设置为当前用户
                if (!auditUserName.val()) {
                    auditUserName.val([[${@permission.getPrincipalProperty('userName')}]]);
                }
                // 标记审核意见为必填
                auditComment.attr('required', true);
                auditComment.closest('.form-group').find('label').addClass('is-required');
            } else {
                // 移除必填标记
                auditComment.removeAttr('required');
                auditComment.closest('.form-group').find('label').removeClass('is-required');
            }
        });

        // 页面加载时检查状态
        $(document).ready(function() {
            $("select[name='requestStatus']").trigger('change');
        });

        // 表单分组样式
        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });
    </script>
</body>
</html>