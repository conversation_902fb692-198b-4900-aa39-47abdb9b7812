<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改缓考申请')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-defer-edit" th:object="${sysAuditRequest}">
            <input name="requestId" th:field="*{requestId}" type="hidden">
            
            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学生姓名：</label>
                    <div class="col-sm-6">
                        <input name="studentName" th:field="*{studentName}" class="form-control" type="text" required placeholder="请输入学生姓名">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学号：</label>
                    <div class="col-sm-6">
                        <input name="studentNo" th:field="*{studentNo}" class="form-control" type="text" required placeholder="请输入学号">
                    </div>
                </div>
            </div>
            
            <!-- 缓考申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">缓考申请信息</h4>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">申请科目：</label>
                    <div class="col-sm-8">
                        <select name="subjectName" class="form-control">
                            <option value="">请选择测试项目（不选择表示全部项目）</option>
                            <option value="身高体重BMI" th:selected="*{subjectName == '身高体重BMI'}">身高体重BMI</option>
                            <option value="肺活量" th:selected="*{subjectName == '肺活量'}">肺活量</option>
                            <option value="50米跑" th:selected="*{subjectName == '50米跑'}">50米跑</option>
                            <option value="立定跳远" th:selected="*{subjectName == '立定跳远'}">立定跳远</option>
                            <option value="坐位体前屈" th:selected="*{subjectName == '坐位体前屈'}">坐位体前屈</option>
                            <option value="仰卧起坐" th:selected="*{subjectName == '仰卧起坐'}">仰卧起坐(女)</option>
                            <option value="引体向上" th:selected="*{subjectName == '引体向上'}">引体向上(男)</option>
                            <option value="800米跑" th:selected="*{subjectName == '800米跑'}">800米跑(女)</option>
                            <option value="1000米跑" th:selected="*{subjectName == '1000米跑'}">1000米跑(男)</option>
                            <option value="全部项目" th:selected="*{subjectName == '全部项目'}">全部项目</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">申请原因：</label>
                    <div class="col-sm-8">
                        <select name="requestReason" class="form-control" required>
                            <option value="">请选择申请原因</option>
                            <option value="illness" th:selected="*{requestReason == 'illness'}">疾病</option>
                            <option value="injury" th:selected="*{requestReason == 'injury'}">受伤</option>
                            <option value="emergency" th:selected="*{requestReason == 'emergency'}">紧急事务</option>
                            <option value="other" th:selected="*{requestReason == 'other'}">其他</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">详细说明：</label>
                    <div class="col-sm-8">
                        <textarea name="description" class="form-control" rows="4" required>[[*{description}]]</textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件URL：</label>
                    <div class="col-sm-8">
                        <textarea name="attachmentUrls" class="form-control" rows="2">[[*{attachmentUrls}]]</textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" rows="2">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/defer";
        
        $("#form-defer-edit").validate({
            focusCleanup: true,
            rules: {
                studentName: { required: true, maxlength: 100 },
                studentNo: { required: true, maxlength: 50 },
                requestReason: { required: true },
                description: { required: true, minlength: 10 }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-defer-edit').serialize());
            }
        }

        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });
    </script>
</body>
</html>
