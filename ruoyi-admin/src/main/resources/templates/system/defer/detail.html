<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('缓考申请详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal" th:object="${sysAuditRequest}">
            
            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">学生姓名：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{studentName}"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">学号：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{studentNo}"></p>
                    </div>
                </div>
            </div>
            
            <!-- 缓考申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">缓考申请信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请科目：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{subjectName} ?: '全部项目'"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请原因：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static">
                            <span th:if="*{requestReason == 'illness'}" class="label label-danger">疾病</span>
                            <span th:if="*{requestReason == 'injury'}" class="label label-warning">受伤</span>
                            <span th:if="*{requestReason == 'emergency'}" class="label label-info">紧急事务</span>
                            <span th:if="*{requestReason == 'other'}" class="label label-default">其他</span>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">详细说明：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm" th:text="*{description}"></div>
                    </div>
                </div>
            </div>
            
            <!-- 附件信息区域 -->
            <div class="form-group" th:if="*{attachmentUrls != null and attachmentUrls != ''}">
                <div class="col-xs-12">
                    <h4 class="form-header h4">证明材料</h4>
                </div>
            </div>
            
            <div class="col-xs-12" th:if="*{attachmentUrls != null and attachmentUrls != ''}">
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件链接：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm">
                            <div th:each="url,iterStat : ${#strings.listSplit(sysAuditRequest.attachmentUrls, ',')}">
                                <a th:href="${url}" target="_blank" class="btn btn-link btn-sm">
                                    <i class="fa fa-paperclip"></i> 附件[[${iterStat.count}]]
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 申请状态区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">申请状态</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">申请状态：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static">
                            <span th:if="*{requestStatus == 'pending'}" class="label label-warning">待审核</span>
                            <span th:if="*{requestStatus == 'approved'}" class="label label-success">审核通过</span>
                            <span th:if="*{requestStatus == 'rejected'}" class="label label-danger">审核驳回</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">提交时间：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="${#dates.format(sysAuditRequest.submitTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                    </div>
                </div>
            </div>
            
            <!-- 审核信息区域 -->
            <div class="form-group" th:if="*{auditUserName != null or auditTime != null or auditComment != null}">
                <div class="col-xs-12">
                    <h4 class="form-header h4">审核信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6" th:if="*{auditUserName != null}">
                <div class="form-group">
                    <label class="col-sm-6 control-label">审核人：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="*{auditUserName}"></p>
                    </div>
                </div>
            </div>
            <div class="col-xs-6" th:if="*{auditTime != null}">
                <div class="form-group">
                    <label class="col-sm-6 control-label">审核时间：</label>
                    <div class="col-sm-6">
                        <p class="form-control-static" th:text="${#dates.format(sysAuditRequest.auditTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12" th:if="*{auditComment != null}">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核意见：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm" th:text="*{auditComment}"></div>
                    </div>
                </div>
            </div>
            
            <!-- 备注信息 -->
            <div class="col-xs-12" th:if="*{remark != null and remark != ''}">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm" th:text="*{remark}"></div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="col-xs-12">
                <div class="form-group">
                    <div class="col-sm-offset-3 col-sm-8">
                        <button type="button" class="btn btn-default" onclick="$.modal.closeTab()">
                            <i class="fa fa-reply"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // 表单分组样式
        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });
    </script>
</body>
</html>
