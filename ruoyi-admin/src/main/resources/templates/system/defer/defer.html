<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('缓考申请列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学生姓名：</label>
                                <input type="text" name="studentName" placeholder="请输入学生姓名"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentNo" placeholder="请输入学号"/>
                            </li>
                            <li>
                                <label>申请科目：</label>
                                <select name="subjectName">
                                    <option value="">所有科目</option>
                                    <option value="身高体重BMI">身高体重BMI</option>
                                    <option value="肺活量">肺活量</option>
                                    <option value="50米跑">50米跑</option>
                                    <option value="立定跳远">立定跳远</option>
                                    <option value="坐位体前屈">坐位体前屈</option>
                                    <option value="仰卧起坐">仰卧起坐(女)</option>
                                    <option value="引体向上">引体向上(男)</option>
                                    <option value="800米跑">800米跑(女)</option>
                                    <option value="1000米跑">1000米跑(男)</option>
                                    <option value="全部项目">全部项目</option>
                                </select>
                            </li>
                            <li>
                                <label>申请状态：</label>
                                <select name="requestStatus">
                                    <option value="">所有状态</option>
                                    <option value="pending">待审核</option>
                                    <option value="approved">审核通过</option>
                                    <option value="rejected">审核驳回</option>
                                </select>
                            </li>
                            <li>
                                <label>申请原因：</label>
                                <select name="requestReason">
                                    <option value="">所有原因</option>
                                    <option value="illness">疾病</option>
                                    <option value="injury">受伤</option>
                                    <option value="emergency">紧急事务</option>
                                    <option value="other">其他</option>
                                </select>
                            </li>
                            <li>
                                <label>提交时间：</label>
                                <input type="text" class="time-input" placeholder="请选择提交时间" name="submitTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:defer:add">
                    <i class="fa fa-plus"></i> 申请缓考
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:defer:edit">
                    <i class="fa fa-edit"></i> 修改申请
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:defer:remove">
                    <i class="fa fa-remove"></i> 删除申请
                </a>
                <a class="btn btn-info single disabled" onclick="$.operate.detail()" shiro:hasPermission="system:defer:detail">
                    <i class="fa fa-search"></i> 查看详情
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:defer:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:defer:remove')}]];
        var detailFlag = [[${@permission.hasPermi('system:defer:detail')}]];
        var prefix = ctx + "system/defer";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "缓考申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'requestId',
                    title: '申请ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名',
                    align: 'center'
                },
                {
                    field: 'studentNo',
                    title: '学号',
                    align: 'center'
                },
                {
                    field: 'subjectName',
                    title: '申请科目',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value || '全部项目';
                    }
                },
                {
                    field: 'requestReason',
                    title: '申请原因',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var reasonNames = {
                            'illness': '<span class="label label-danger">疾病</span>',
                            'injury': '<span class="label label-warning">受伤</span>',
                            'emergency': '<span class="label label-info">紧急事务</span>',
                            'other': '<span class="label label-default">其他</span>'
                        };
                        return reasonNames[value] || value;
                    }
                },
                {
                    field: 'description',
                    title: '详细说明',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'requestStatus',
                    title: '申请状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === 'pending') {
                            return '<span class="label label-warning">待审核</span>';
                        } else if (value === 'approved') {
                            return '<span class="label label-success">审核通过</span>';
                        } else if (value === 'rejected') {
                            return '<span class="label label-danger">审核驳回</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'submitTime',
                    title: '提交时间',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '-';
                    }
                },
                {
                    field: 'auditUserName',
                    title: '审核人',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'auditTime',
                    title: '审核时间',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '-';
                    }
                },
                {
                    field: 'auditComment',
                    title: '审核意见',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        // 只有待审核状态的申请才能修改
                        if (row.requestStatus === 'pending') {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.requestId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.requestId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        }
                        
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.requestId + '\')"><i class="fa fa-search"></i>详情</a>');
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 查看详情 */
        $.operate.detail = function(id) {
            var url = prefix + "/detail/" + $.operate.getIdByRow(id);
            $.modal.openTab("缓考申请详情", url);
        }
    </script>
</body>
</html>
