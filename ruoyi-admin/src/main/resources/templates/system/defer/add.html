<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增缓考申请')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-defer-add">
            <!-- 学生信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">学生信息</h4>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学生姓名：</label>
                    <div class="col-sm-6">
                        <input name="studentName" class="form-control" type="text" required placeholder="学生姓名" readonly>
                        <input name="studentId" type="hidden">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">学号：</label>
                    <div class="col-sm-6">
                        <input name="studentNo" class="form-control" type="text" required placeholder="请输入学号">
                    </div>
                </div>
            </div>
            
            <!-- 缓考申请信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">缓考申请信息</h4>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">申请科目：</label>
                    <div class="col-sm-8">
                        <select name="subjectName" class="form-control">
                            <option value="">请选择测试项目（不选择表示全部项目）</option>
                            <option value="身高体重BMI">身高体重BMI</option>
                            <option value="肺活量">肺活量</option>
                            <option value="50米跑">50米跑</option>
                            <option value="立定跳远">立定跳远</option>
                            <option value="坐位体前屈">坐位体前屈</option>
                            <option value="仰卧起坐">仰卧起坐(女)</option>
                            <option value="引体向上">引体向上(男)</option>
                            <option value="800米跑">800米跑(女)</option>
                            <option value="1000米跑">1000米跑(男)</option>
                            <option value="全部项目">全部项目</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 选择需要申请缓考的具体项目</span>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">申请原因：</label>
                    <div class="col-sm-8">
                        <select name="requestReason" class="form-control" required>
                            <option value="">请选择申请原因</option>
                            <option value="illness">疾病</option>
                            <option value="injury">受伤</option>
                            <option value="emergency">紧急事务</option>
                            <option value="other">其他</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请根据实际情况选择申请原因</span>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">详细说明：</label>
                    <div class="col-sm-8">
                        <textarea name="description" class="form-control" rows="4" required placeholder="请详细说明申请缓考的具体情况，如病情描述、受伤部位、紧急事务性质等"></textarea>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请提供详细的情况说明，有助于审核</span>
                    </div>
                </div>
            </div>
            
            <!-- 附件信息区域 -->
            <div class="form-group">
                <div class="col-xs-12">
                    <h4 class="form-header h4">证明材料</h4>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">证明材料：</label>
                    <div class="col-sm-8">
                        <div class="file-upload-container">
                            <input type="file" id="evidenceFile" name="evidenceFile" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt" style="display: none;">
                            <button type="button" class="btn btn-primary" onclick="$('#evidenceFile').click();">
                                <i class="fa fa-upload"></i> 选择文件
                            </button>
                            <span class="file-info">支持jpg、png、pdf、doc、docx格式，最多5个文件</span>
                        </div>
                        <input type="hidden" name="attachmentUrls" id="attachmentUrls">
                        <div class="file-list" id="fileList" style="margin-top: 10px;"></div>
                        <span class="help-block m-b-none">
                            <i class="fa fa-info-circle"></i>
                            请上传医院证明、诊断书、请假条等相关证明材料<br>
                            <strong>常见证明材料：</strong>疾病-医院诊断证明；受伤-医院检查报告；紧急事务-相关证明文件
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/defer";

        // 页面加载时初始化
        $(function() {
            loadCurrentUser();
            initFileUpload();
        });

        // 加载当前用户信息
        function loadCurrentUser() {
            console.log("开始加载当前用户信息，接口地址：" + prefix + "/currentUser");
            $.post(prefix + "/currentUser", {}, function(result) {
                console.log("当前用户接口返回：", result);
                if (result.code == 0) {
                    // 自动填充当前用户信息
                    $("input[name='studentName']").val(result.data.userName);
                    $("input[name='studentNo']").val(result.data.loginName);
                    $("input[name='studentId']").val(result.data.userId);
                } else {
                    console.error("获取当前用户信息失败：", result);
                    $.modal.msgError("获取当前用户信息失败：" + result.msg);
                }
            }).fail(function(xhr, status, error) {
                console.error("当前用户接口请求失败：", xhr, status, error);
                $.modal.msgError("网络请求失败，请检查网络连接");
            });
        }

        // 初始化文件上传
        function initFileUpload() {
            $("#evidenceFile").change(function() {
                var files = this.files;
                if (files.length > 0) {
                    if (files.length > 5) {
                        $.modal.msgError("最多只能上传5个文件");
                        return;
                    }
                    uploadFiles(files);
                }
            });
        }

        // 文件上传处理
        function uploadFiles(files) {
            var formData = new FormData();
            for (var i = 0; i < files.length; i++) {
                formData.append("files", files[i]);
            }

            $.modal.loading("正在上传文件...");

            $.ajax({
                url: ctx + "common/uploads",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        // 保存文件URL
                        var urls = result.urls.join(",");
                        $("#attachmentUrls").val(urls);

                        // 显示文件列表
                        showFileList(result.fileNames, result.originalFilenames);
                        $.modal.msgSuccess("文件上传成功");
                    } else {
                        $.modal.msgError("文件上传失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.msgError("文件上传失败");
                }
            });
        }

        // 显示文件列表
        function showFileList(fileNames, originalNames) {
            var fileListHtml = "";
            for (var i = 0; i < fileNames.length; i++) {
                fileListHtml += '<div class="file-item" style="margin: 5px 0; padding: 8px; border: 1px solid #e7eaec; border-radius: 3px; background: #f9f9f9;">';
                fileListHtml += '<i class="fa fa-file-o" style="margin-right: 5px;"></i>';
                fileListHtml += '<span class="file-name">' + originalNames[i] + '</span>';
                fileListHtml += '<a href="' + ctx + 'common/download?fileName=' + encodeURIComponent(fileNames[i]) + '" class="btn btn-xs btn-info" style="margin-left: 10px;" target="_blank">下载</a>';
                fileListHtml += '</div>';
            }
            $("#fileList").html(fileListHtml);
        }

        $("#form-defer-add").validate({
            focusCleanup: true,
            rules: {
                studentName: {
                    required: true,
                    maxlength: 100
                },
                studentNo: {
                    required: true,
                    maxlength: 50
                },
                requestReason: {
                    required: true
                },
                description: {
                    required: true,
                    minlength: 10
                }
            },
            messages: {
                studentName: {
                    required: "学生姓名不能为空",
                    maxlength: "学生姓名不能超过100个字符"
                },
                studentNo: {
                    required: "请输入学号",
                    maxlength: "学号不能超过50个字符"
                },
                requestReason: {
                    required: "请选择申请原因"
                },
                description: {
                    required: "请输入详细说明",
                    minlength: "详细说明至少需要10个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-defer-add').serialize());
            }
        }

        // 申请原因变化时的提示
        $("select[name='requestReason']").change(function() {
            var reason = $(this).val();
            var hints = {
                'illness': '疾病：请在详细说明中描述病情，并提供医院诊断证明链接',
                'injury': '受伤：请在详细说明中描述受伤情况和康复时间，并提供医院检查报告链接',
                'emergency': '紧急事务：请在详细说明中具体描述紧急事务的性质和处理时间',
                'other': '其他：请在详细说明中具体描述情况，并提供相关证明材料'
            };
            if (reason && hints[reason]) {
                $.modal.msg(hints[reason]);
            }
        });

        // 表单分组样式
        $(".form-header").css({
            'color': '#333',
            'border-bottom': '2px solid #e7eaec',
            'padding-bottom': '10px',
            'margin-bottom': '20px',
            'margin-top': '20px'
        });

        // 文件上传样式
        $(".file-upload-container").css({
            'border': '2px dashed #e7eaec',
            'padding': '20px',
            'text-align': 'center',
            'border-radius': '5px',
            'background': '#fafafa'
        });

        $(".file-info").css({
            'margin-left': '10px',
            'color': '#999',
            'font-size': '12px'
        });
    </script>
</body>
</html>
