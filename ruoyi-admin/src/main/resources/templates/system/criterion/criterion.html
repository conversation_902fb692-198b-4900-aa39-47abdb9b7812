<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成绩标准列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>考核项目：</label>
                                <select name="itemId" th:with="type=${@dict.getType('sys_test_item')}">
                                    <option value="">所有项目</option>
                                    <option value="1">身高体重BMI</option>
                                    <option value="2">肺活量</option>
                                    <option value="3">50米跑</option>
                                    <option value="4">立定跳远</option>
                                    <option value="5">坐位体前屈</option>
                                    <option value="6">仰卧起坐(女)</option>
                                    <option value="7">引体向上(男)</option>
                                    <option value="8">800米跑(女)</option>
                                    <option value="9">1000米跑(男)</option>
                                </select>
                            </li>
                            <li>
                                <label>性别：</label>
                                <select name="gender">
                                    <option value="">所有性别</option>
                                    <option value="M">男</option>
                                    <option value="F">女</option>
                                </select>
                            </li>
                            <li>
                                <label>年级：</label>
                                <select name="gradeLevel">
                                    <option value="">所有年级</option>
                                    <option value="freshman">大一</option>
                                    <option value="sophomore">大二</option>
                                    <option value="junior">大三</option>
                                    <option value="senior">大四</option>
                                </select>
                            </li>
                            <li>
                                <label>生效日期：</label>
                                <input type="text" class="time-input" placeholder="请选择生效日期" name="effectiveDate"/>
                            </li>
                            <li>
                                <label>版本号：</label>
                                <input type="text" name="version" placeholder="请输入版本号"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:criterion:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:criterion:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:criterion:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:criterion:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:criterion:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:criterion:remove')}]];
        var prefix = ctx + "system/criterion";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "成绩标准",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'criterionId',
                    title: '标准ID',
                    visible: false
                },
                {
                    field: 'itemId',
                    title: '考核项目',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var itemNames = {
                            '1': '身高体重BMI',
                            '2': '肺活量',
                            '3': '50米跑',
                            '4': '立定跳远',
                            '5': '坐位体前屈',
                            '6': '仰卧起坐(女)',
                            '7': '引体向上(男)',
                            '8': '800米跑(女)',
                            '9': '1000米跑(男)'
                        };
                        return itemNames[value] || '未知项目(' + value + ')';
                    }
                },
                {
                    field: 'gender',
                    title: '性别',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === 'M') {
                            return '<span class="label label-primary">男</span>';
                        } else if (value === 'F') {
                            return '<span class="label label-danger">女</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'gradeLevel',
                    title: '年级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var gradeNames = {
                            'freshman': '大一',
                            'sophomore': '大二',
                            'junior': '大三',
                            'senior': '大四'
                        };
                        return gradeNames[value] || value;
                    }
                },
                {
                    field: 'excellentMin',
                    title: '优秀',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (row.excellentMin != null && row.excellentMax != null) {
                            return row.excellentMin + ' ~ ' + row.excellentMax;
                        }
                        return '-';
                    }
                },
                {
                    field: 'goodMin',
                    title: '良好',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (row.goodMin != null && row.goodMax != null) {
                            return row.goodMin + ' ~ ' + row.goodMax;
                        }
                        return '-';
                    }
                },
                {
                    field: 'passMin',
                    title: '及格',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (row.passMin != null && row.passMax != null) {
                            return row.passMin + ' ~ ' + row.passMax;
                        }
                        return '-';
                    }
                },
                {
                    field: 'isReverse',
                    title: '计算方式',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '1') {
                            return '<span class="label label-warning">反向</span>';
                        } else {
                            return '<span class="label label-success">正向</span>';
                        }
                    }
                },
                {
                    field: 'effectiveDate',
                    title: '生效日期',
                    align: 'center'
                },
                {
                    field: 'version',
                    title: '版本',
                    align: 'center'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.criterionId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.criterionId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>