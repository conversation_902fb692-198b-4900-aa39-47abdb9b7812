<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增成绩标准')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-criterion-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">考核项目：</label>
                    <div class="col-sm-8">
                        <select name="itemId" class="form-control" required>
                            <option value="">请选择考核项目</option>
                            <option value="1">身高体重BMI</option>
                            <option value="2">肺活量</option>
                            <option value="3">50米跑</option>
                            <option value="4">立定跳远</option>
                            <option value="5">坐位体前屈</option>
                            <option value="6">仰卧起坐(女)</option>
                            <option value="7">引体向上(男)</option>
                            <option value="8">800米跑(女)</option>
                            <option value="9">1000米跑(男)</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">性别：</label>
                    <div class="col-sm-8">
                        <select name="gender" class="form-control" required>
                            <option value="">请选择性别</option>
                            <option value="M">男</option>
                            <option value="F">女</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">年级：</label>
                    <div class="col-sm-8">
                        <select name="gradeLevel" class="form-control" required>
                            <option value="">请选择年级</option>
                            <option value="freshman">大一</option>
                            <option value="sophomore">大二</option>
                            <option value="junior">大三</option>
                            <option value="senior">大四</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">优秀最低值：</label>
                    <div class="col-sm-6">
                        <input name="excellentMin" class="form-control" type="number" step="0.01" placeholder="如：90.00">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">优秀最高值：</label>
                    <div class="col-sm-6">
                        <input name="excellentMax" class="form-control" type="number" step="0.01" placeholder="如：100.00">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">良好最低值：</label>
                    <div class="col-sm-6">
                        <input name="goodMin" class="form-control" type="number" step="0.01" placeholder="如：80.00">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">良好最高值：</label>
                    <div class="col-sm-6">
                        <input name="goodMax" class="form-control" type="number" step="0.01" placeholder="如：89.99">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">及格最低值：</label>
                    <div class="col-sm-6">
                        <input name="passMin" class="form-control" type="number" step="0.01" placeholder="如：60.00">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">及格最高值：</label>
                    <div class="col-sm-6">
                        <input name="passMax" class="form-control" type="number" step="0.01" placeholder="如：79.99">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">不及格最低值：</label>
                    <div class="col-sm-6">
                        <input name="failMin" class="form-control" type="number" step="0.01" placeholder="如：0.00">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">不及格最高值：</label>
                    <div class="col-sm-6">
                        <input name="failMax" class="form-control" type="number" step="0.01" placeholder="如：59.99">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">计算方式：</label>
                    <div class="col-sm-8">
                        <select name="isReverse" class="form-control">
                            <option value="0">正向计算（数值越大越好，如跳远距离）</option>
                            <option value="1">反向计算（数值越小越好，如跑步时间）</option>
                        </select>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 正向：分数高表示成绩好；反向：时间短表示成绩好</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label is-required">生效日期：</label>
                    <div class="col-sm-6">
                        <div class="input-group date">
                            <input name="effectiveDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-6 control-label">失效日期：</label>
                    <div class="col-sm-6">
                        <div class="input-group date">
                            <input name="expireDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 不填表示长期有效</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">版本号：</label>
                    <div class="col-sm-8">
                        <input name="version" class="form-control" type="text" placeholder="如：1.0" value="1.0">
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 用于区分同一项目的不同版本标准</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" rows="3" placeholder="请输入备注信息，如：适用范围、特殊说明等"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/criterion"

        $("#form-criterion-add").validate({
            focusCleanup: true,
            rules: {
                itemId: {
                    required: true
                },
                gender: {
                    required: true
                },
                gradeLevel: {
                    required: true
                },
                effectiveDate: {
                    required: true
                },
                excellentMin: {
                    number: true
                },
                excellentMax: {
                    number: true
                },
                goodMin: {
                    number: true
                },
                goodMax: {
                    number: true
                },
                passMin: {
                    number: true
                },
                passMax: {
                    number: true
                },
                failMin: {
                    number: true
                },
                failMax: {
                    number: true
                }
            },
            messages: {
                itemId: {
                    required: "请选择考核项目"
                },
                gender: {
                    required: "请选择性别"
                },
                gradeLevel: {
                    required: "请选择年级"
                },
                effectiveDate: {
                    required: "请选择生效日期"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 验证分数区间的合理性
                if (!validateScoreRanges()) {
                    return false;
                }
                $.operate.save(prefix + "/add", $('#form-criterion-add').serialize());
            }
        }

        // 验证分数区间合理性
        function validateScoreRanges() {
            var excellentMin = parseFloat($("input[name='excellentMin']").val()) || 0;
            var excellentMax = parseFloat($("input[name='excellentMax']").val()) || 0;
            var goodMin = parseFloat($("input[name='goodMin']").val()) || 0;
            var goodMax = parseFloat($("input[name='goodMax']").val()) || 0;
            var passMin = parseFloat($("input[name='passMin']").val()) || 0;
            var passMax = parseFloat($("input[name='passMax']").val()) || 0;
            var failMin = parseFloat($("input[name='failMin']").val()) || 0;
            var failMax = parseFloat($("input[name='failMax']").val()) || 0;

            // 检查每个等级的最小值是否小于等于最大值
            if (excellentMin > 0 && excellentMax > 0 && excellentMin > excellentMax) {
                $.modal.alertWarning("优秀等级的最低值不能大于最高值");
                return false;
            }
            if (goodMin > 0 && goodMax > 0 && goodMin > goodMax) {
                $.modal.alertWarning("良好等级的最低值不能大于最高值");
                return false;
            }
            if (passMin > 0 && passMax > 0 && passMin > passMax) {
                $.modal.alertWarning("及格等级的最低值不能大于最高值");
                return false;
            }
            if (failMin > 0 && failMax > 0 && failMin > failMax) {
                $.modal.alertWarning("不及格等级的最低值不能大于最高值");
                return false;
            }

            return true;
        }

        // 日期选择器
        $("input[name='effectiveDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true,
            todayBtn: true
        });

        $("input[name='expireDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true,
            todayBtn: true
        });

        // 项目选择变化时的提示
        $("select[name='itemId']").change(function() {
            var itemId = $(this).val();
            var itemText = $(this).find("option:selected").text();
            if (itemId) {
                // 根据项目类型给出单位提示
                var unitHint = getUnitHint(itemId);
                if (unitHint) {
                    $.modal.msg("提示：" + itemText + " 的计量单位为 " + unitHint);
                }
            }
        });

        // 获取单位提示
        function getUnitHint(itemId) {
            var units = {
                '1': 'BMI指数',
                '2': '毫升(ml)',
                '3': '秒',
                '4': '厘米(cm)',
                '5': '厘米(cm)',
                '6': '个/分钟',
                '7': '个',
                '8': '秒',
                '9': '秒'
            };
            return units[itemId] || '';
        }
    </script>
</body>
</html>