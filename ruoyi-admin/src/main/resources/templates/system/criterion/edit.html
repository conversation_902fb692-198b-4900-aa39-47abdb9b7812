<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改成绩标准')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-criterion-edit" th:object="${sysScoreCriterion}">
            <input name="criterionId" th:field="*{criterionId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">性别(M男F女)：</label>
                    <div class="col-sm-8">
                        <input name="gender" th:field="*{gender}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">年级(freshman,sophomore,junior,senior)：</label>
                    <div class="col-sm-8">
                        <input name="gradeLevel" th:field="*{gradeLevel}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">优秀最低值：</label>
                    <div class="col-sm-8">
                        <input name="excellentMin" th:field="*{excellentMin}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">优秀最高值：</label>
                    <div class="col-sm-8">
                        <input name="excellentMax" th:field="*{excellentMax}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">良好最低值：</label>
                    <div class="col-sm-8">
                        <input name="goodMin" th:field="*{goodMin}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">良好最高值：</label>
                    <div class="col-sm-8">
                        <input name="goodMax" th:field="*{goodMax}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">及格最低值：</label>
                    <div class="col-sm-8">
                        <input name="passMin" th:field="*{passMin}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">及格最高值：</label>
                    <div class="col-sm-8">
                        <input name="passMax" th:field="*{passMax}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">不及格最低值：</label>
                    <div class="col-sm-8">
                        <input name="failMin" th:field="*{failMin}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">不及格最高值：</label>
                    <div class="col-sm-8">
                        <input name="failMax" th:field="*{failMax}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否反向计算(0正向1反向,如跑步时间越少越好)：</label>
                    <div class="col-sm-8">
                        <input name="isReverse" th:field="*{isReverse}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">生效日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="effectiveDate" th:value="${#dates.format(sysScoreCriterion.effectiveDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">失效日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="expireDate" th:value="${#dates.format(sysScoreCriterion.expireDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">版本号：</label>
                    <div class="col-sm-8">
                        <input name="version" th:field="*{version}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/criterion";
        $("#form-criterion-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-criterion-edit').serialize());
            }
        }

        $("input[name='effectiveDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='expireDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>