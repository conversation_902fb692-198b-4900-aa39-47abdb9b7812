<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改学生成绩')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-score-edit" th:object="${sysStudentScore}">
            <input name="scoreId" th:field="*{scoreId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">学生姓名：</label>
                    <div class="col-sm-8">
                        <input name="studentName" th:field="*{studentName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">学号：</label>
                    <div class="col-sm-8">
                        <input name="studentId" th:field="*{studentId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">班级名称：</label>
                    <div class="col-sm-8">
                        <input name="className" th:field="*{className}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">考核项目：</label>
                    <div class="col-sm-8">
                        <select name="examItem" class="form-control" required>
                            <option value="">请选择考核项目</option>
                            <option value="100米跑" th:selected="${sysStudentScore.examItem == '100米跑'}">100米跑</option>
                            <option value="仰卧起坐" th:selected="${sysStudentScore.examItem == '仰卧起坐'}">仰卧起坐</option>
                            <option value="肺活量" th:selected="${sysStudentScore.examItem == '肺活量'}">肺活量</option>
                            <option value="立定跳远" th:selected="${sysStudentScore.examItem == '立定跳远'}">立定跳远</option>
                            <option value="引体向上" th:selected="${sysStudentScore.examItem == '引体向上'}">引体向上</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">成绩：</label>
                    <div class="col-sm-8">
                        <input name="score" th:field="*{score}" class="form-control" type="number" step="0.01" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">成绩等级：</label>
                    <div class="col-sm-8">
                        <select name="scoreLevel" class="form-control" required>
                            <option value="">请选择成绩等级</option>
                            <option value="优秀" th:selected="${sysStudentScore.scoreLevel == '优秀'}">优秀</option>
                            <option value="良好" th:selected="${sysStudentScore.scoreLevel == '良好'}">良好</option>
                            <option value="及格" th:selected="${sysStudentScore.scoreLevel == '及格'}">及格</option>
                            <option value="不及格" th:selected="${sysStudentScore.scoreLevel == '不及格'}">不及格</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">考试学期：</label>
                    <div class="col-sm-8">
                        <select name="examSemester" class="form-control" required>
                            <option value="">请选择考试学期</option>
                            <option value="2024-1" th:selected="${sysStudentScore.examSemester == '2024-1'}">2024年第一学期</option>
                            <option value="2024-2" th:selected="${sysStudentScore.examSemester == '2024-2'}">2024年第二学期</option>
                            <option value="2023-1" th:selected="${sysStudentScore.examSemester == '2023-1'}">2023年第一学期</option>
                            <option value="2023-2" th:selected="${sysStudentScore.examSemester == '2023-2'}">2023年第二学期</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">考试日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="examDate" th:value="${#dates.format(sysStudentScore.examDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">状态：</label>
                    <div class="col-sm-8">
                        <select name="status" class="form-control">
                            <option value="1" th:selected="${sysStudentScore.status == '1'}">有效</option>
                            <option value="0" th:selected="${sysStudentScore.status == '0'}">无效</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control" placeholder="请输入备注信息">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/score";
        $("#form-score-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-score-edit').serialize());
            }
        }

        $("input[name='examDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>