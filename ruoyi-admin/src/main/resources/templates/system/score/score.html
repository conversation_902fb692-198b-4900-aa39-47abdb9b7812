<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学生成绩列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学生姓名：</label>
                                <input type="text" name="studentName" placeholder="请输入学生姓名"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentId" placeholder="请输入学号"/>
                            </li>
                            <li>
                                <label>班级：</label>
                                <input type="text" name="className" placeholder="请输入班级名称"/>
                            </li>
                            <li>
                                <label>考核项目：</label>
                                <select name="examItem" class="form-control">
                                    <option value="">全部项目</option>
                                    <option value="100米跑">100米跑</option>
                                    <option value="仰卧起坐">仰卧起坐</option>
                                    <option value="肺活量">肺活量</option>
                                    <option value="立定跳远">立定跳远</option>
                                    <option value="引体向上">引体向上</option>
                                </select>
                            </li>
                            <li>
                                <label>考试学期：</label>
                                <select name="examSemester" class="form-control">
                                    <option value="">全部学期</option>
                                    <option value="2024-1">2024年第一学期</option>
                                    <option value="2024-2">2024年第二学期</option>
                                    <option value="2023-1">2023年第一学期</option>
                                    <option value="2023-2">2023年第二学期</option>
                                </select>
                            </li>
                            <li>
                                <label>成绩等级：</label>
                                <select name="scoreLevel" class="form-control">
                                    <option value="">全部等级</option>
                                    <option value="优秀">优秀</option>
                                    <option value="良好">良好</option>
                                    <option value="及格">及格</option>
                                    <option value="不及格">不及格</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:score:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:score:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:score:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:score:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:score:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:score:remove')}]];
        var prefix = ctx + "system/score";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "学生成绩",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'scoreId',
                    title: '成绩ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名',
                    align: 'center'
                },
                {
                    field: 'studentId',
                    title: '学号',
                    align: 'center'
                },
                {
                    field: 'className',
                    title: '班级',
                    align: 'center'
                },
                {
                    field: 'examItem',
                    title: '考核项目',
                    align: 'center'
                },
                {
                    field: 'score',
                    title: '成绩',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="text-primary"><strong>' + value + '</strong></span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'scoreLevel',
                    title: '等级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '优秀') {
                            return '<span class="badge badge-success">优秀</span>';
                        } else if (value == '良好') {
                            return '<span class="badge badge-info">良好</span>';
                        } else if (value == '及格') {
                            return '<span class="badge badge-warning">及格</span>';
                        } else if (value == '不及格') {
                            return '<span class="badge badge-danger">不及格</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'examSemester',
                    title: '考试学期',
                    align: 'center'
                },
                {
                    field: 'examDate',
                    title: '考试日期',
                    align: 'center'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-success">有效</span>';
                        } else {
                            return '<span class="badge badge-secondary">无效</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.scoreId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.scoreId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>