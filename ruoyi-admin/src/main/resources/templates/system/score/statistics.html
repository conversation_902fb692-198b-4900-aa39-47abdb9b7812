<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('成绩统计分析')" />
    <th:block th:include="include :: bootstrap-table-css" />
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stats-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stats-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stats-card.danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-left: 4px solid #007bff;
            padding-left: 10px;
        }
        .chart-box {
            height: 400px;
        }
        .filter-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>成绩统计分析</h5>
                    </div>
                    <div class="ibox-content">
                        
                        <!-- 筛选面板 -->
                        <div class="filter-panel">
                            <form class="form-inline">
                                <div class="form-group">
                                    <label>学期：</label>
                                    <select class="form-control" id="semesterSelect">
                                        <option value="">全部学期</option>
                                        <option value="2024-1" selected>2024年第一学期</option>
                                        <option value="2024-2">2024年第二学期</option>
                                        <option value="2023-1">2023年第一学期</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-left: 20px;">
                                    <label>年级：</label>
                                    <select class="form-control" id="gradeSelect">
                                        <option value="">全部年级</option>
                                        <option value="freshman">大一</option>
                                        <option value="sophomore">大二</option>
                                        <option value="junior">大三</option>
                                        <option value="senior">大四</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-left: 20px;">
                                    <label>考核项目：</label>
                                    <select class="form-control" id="itemSelect">
                                        <option value="">全部项目</option>
                                        <option value="run100">100米跑</option>
                                        <option value="situp">仰卧起坐</option>
                                        <option value="lung">肺活量</option>
                                        <option value="jump">立定跳远</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="refreshCharts()" style="margin-left: 20px;">
                                    <i class="fa fa-search"></i> 查询
                                </button>
                            </form>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalStudents">1,248</div>
                                    <div class="stats-label">参与学生总数</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card success">
                                    <div class="stats-number" id="excellentRate">68.5%</div>
                                    <div class="stats-label">优秀率</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card warning">
                                    <div class="stats-number" id="passRate">92.3%</div>
                                    <div class="stats-label">及格率</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card danger">
                                    <div class="stats-number" id="avgScore">85.2</div>
                                    <div class="stats-label">平均分</div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="row">
                            <!-- 成绩分布饼图 -->
                            <div class="col-lg-6">
                                <div class="chart-container">
                                    <div class="chart-title">成绩等级分布</div>
                                    <div id="gradeDistributionChart" class="chart-box"></div>
                                </div>
                            </div>
                            
                            <!-- 项目成绩对比柱状图 -->
                            <div class="col-lg-6">
                                <div class="chart-container">
                                    <div class="chart-title">各项目平均成绩对比</div>
                                    <div id="itemComparisonChart" class="chart-box"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 成绩趋势折线图 -->
                            <div class="col-lg-8">
                                <div class="chart-container">
                                    <div class="chart-title">成绩趋势分析</div>
                                    <div id="trendChart" class="chart-box"></div>
                                </div>
                            </div>
                            
                            <!-- 班级排名 -->
                            <div class="col-lg-4">
                                <div class="chart-container">
                                    <div class="chart-title">班级平均成绩排名</div>
                                    <div class="table-responsive" style="height: 400px; overflow-y: auto;">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>班级</th>
                                                    <th>平均分</th>
                                                </tr>
                                            </thead>
                                            <tbody id="classRankingTable">
                                                <!-- 动态生成 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-table-js" />
    
    <script>
        // 图表实例
        let gradeChart, itemChart, trendChart;
        
        $(document).ready(function() {
            initCharts();
            loadClassRanking();
        });

        // 初始化图表
        function initCharts() {
            initGradeDistributionChart();
            initItemComparisonChart();
            initTrendChart();
        }

        // 成绩分布饼图
        function initGradeDistributionChart() {
            gradeChart = echarts.init(document.getElementById('gradeDistributionChart'));
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '成绩分布',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            {value: 854, name: '优秀', itemStyle: {color: '#5cb85c'}},
                            {value: 234, name: '良好', itemStyle: {color: '#5bc0de'}},
                            {value: 135, name: '及格', itemStyle: {color: '#f0ad4e'}},
                            {value: 25, name: '不及格', itemStyle: {color: '#d9534f'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            
            gradeChart.setOption(option);
        }

        // 项目成绩对比柱状图
        function initItemComparisonChart() {
            itemChart = echarts.init(document.getElementById('itemComparisonChart'));
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: ['100米跑', '仰卧起坐', '肺活量', '立定跳远', '引体向上'],
                        axisTick: {
                            alignWithLabel: true
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '平均分'
                    }
                ],
                series: [
                    {
                        name: '平均成绩',
                        type: 'bar',
                        barWidth: '60%',
                        data: [82.5, 88.2, 79.8, 85.6, 76.3],
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#83bff6'},
                                {offset: 0.5, color: '#188df0'},
                                {offset: 1, color: '#188df0'}
                            ])
                        }
                    }
                ]
            };
            
            itemChart.setOption(option);
        }

        // 成绩趋势折线图
        function initTrendChart() {
            trendChart = echarts.init(document.getElementById('trendChart'));

            const option = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['优秀率', '及格率', '平均分']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['2023-09', '2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03']
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '百分比(%)',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    },
                    {
                        type: 'value',
                        name: '平均分',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: '优秀率',
                        type: 'line',
                        yAxisIndex: 0,
                        data: [65.2, 67.8, 69.1, 71.5, 68.9, 70.2, 68.5],
                        itemStyle: {color: '#5cb85c'},
                        smooth: true
                    },
                    {
                        name: '及格率',
                        type: 'line',
                        yAxisIndex: 0,
                        data: [89.5, 91.2, 92.8, 94.1, 91.7, 93.2, 92.3],
                        itemStyle: {color: '#5bc0de'},
                        smooth: true
                    },
                    {
                        name: '平均分',
                        type: 'line',
                        yAxisIndex: 1,
                        data: [82.1, 83.5, 84.2, 86.1, 83.8, 84.9, 85.2],
                        itemStyle: {color: '#f0ad4e'},
                        smooth: true
                    }
                ]
            };

            trendChart.setOption(option);
        }

        // 加载班级排名数据
        function loadClassRanking() {
            const mockData = [
                {rank: 1, className: '计算机2021-1班', avgScore: 89.5},
                {rank: 2, className: '软件工程2021-2班', avgScore: 88.2},
                {rank: 3, className: '信息安全2021-1班', avgScore: 87.8},
                {rank: 4, className: '计算机2021-2班', avgScore: 86.9},
                {rank: 5, className: '软件工程2021-1班', avgScore: 86.1},
                {rank: 6, className: '网络工程2021-1班', avgScore: 85.7},
                {rank: 7, className: '数据科学2021-1班', avgScore: 84.9},
                {rank: 8, className: '人工智能2021-1班', avgScore: 84.2},
                {rank: 9, className: '物联网2021-1班', avgScore: 83.6},
                {rank: 10, className: '电子商务2021-1班', avgScore: 82.8}
            ];

            let html = '';
            mockData.forEach(item => {
                let badgeClass = '';
                if (item.rank <= 3) {
                    badgeClass = item.rank === 1 ? 'badge-warning' : item.rank === 2 ? 'badge-info' : 'badge-success';
                }

                html += `
                    <tr>
                        <td><span class="badge ${badgeClass}">${item.rank}</span></td>
                        <td>${item.className}</td>
                        <td><strong>${item.avgScore}</strong></td>
                    </tr>
                `;
            });

            $('#classRankingTable').html(html);
        }

        // 刷新图表数据
        function refreshCharts() {
            const semester = $('#semesterSelect').val();
            const grade = $('#gradeSelect').val();
            const item = $('#itemSelect').val();

            // 模拟数据更新
            updateStatCards();
            updateCharts();
            loadClassRanking();

            $.modal.msgSuccess("数据已更新");
        }

        // 更新统计卡片
        function updateStatCards() {
            // 模拟随机数据
            $('#totalStudents').text((Math.floor(Math.random() * 500) + 1000).toLocaleString());
            $('#excellentRate').text((Math.random() * 20 + 60).toFixed(1) + '%');
            $('#passRate').text((Math.random() * 10 + 85).toFixed(1) + '%');
            $('#avgScore').text((Math.random() * 15 + 75).toFixed(1));
        }

        // 更新图表数据
        function updateCharts() {
            // 更新饼图数据
            const newPieData = [
                {value: Math.floor(Math.random() * 200) + 700, name: '优秀', itemStyle: {color: '#5cb85c'}},
                {value: Math.floor(Math.random() * 100) + 200, name: '良好', itemStyle: {color: '#5bc0de'}},
                {value: Math.floor(Math.random() * 50) + 100, name: '及格', itemStyle: {color: '#f0ad4e'}},
                {value: Math.floor(Math.random() * 30) + 10, name: '不及格', itemStyle: {color: '#d9534f'}}
            ];

            gradeChart.setOption({
                series: [{
                    data: newPieData
                }]
            });

            // 更新柱状图数据
            const newBarData = [
                Math.random() * 20 + 70,
                Math.random() * 20 + 70,
                Math.random() * 20 + 70,
                Math.random() * 20 + 70,
                Math.random() * 20 + 70
            ].map(val => val.toFixed(1));

            itemChart.setOption({
                series: [{
                    data: newBarData
                }]
            });
        }

        // 窗口大小改变时重新调整图表
        $(window).resize(function() {
            if (gradeChart) gradeChart.resize();
            if (itemChart) itemChart.resize();
            if (trendChart) trendChart.resize();
        });
    </script>
</body>
</html>
