<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 国家学生体质健康标准测试管理平台">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/css/login-modern.css" th:href="@{/css/login-modern.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        label.error { position:inherit; }

        /* 新的登录页面样式 */
        body.signin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .login-container {
            display: flex;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-wrapper {
            display: flex;
            width: 100%;
            max-width: 1000px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            min-height: 600px;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .system-logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .system-title {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .feature-list li {
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.8;
            display: flex;
            align-items: center;
        }

        .feature-list li::before {
            content: '✓';
            margin-right: 10px;
            color: #4CAF50;
            font-weight: bold;
        }

        .login-form-container {
            max-width: 350px;
            width: 100%;
        }

        .login-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-control {
            height: 50px !important;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            outline: none;
        }

        .btn-login {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .captcha-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-image {
            width: 120px;
            height: 50px;
            border-radius: 8px;
            border: 2px solid #e1e5e9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .captcha-image:hover {
            border-color: #667eea;
        }

        .checkbox-custom {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .checkbox-custom input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkbox-custom label {
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        .login-footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 12px;
        }

        .copyright {
            position: absolute;
            bottom: 20px;
            left: 40px;
            right: 40px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            z-index: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
                margin: 10px;
                min-height: auto;
            }

            .login-left {
                padding: 40px 30px;
                min-height: 200px;
            }

            .login-right {
                padding: 40px 30px;
            }

            .copyright {
                position: static;
                margin-top: 20px;
            }
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="login-container">
        <div class="login-wrapper">
            <!-- 左侧信息区域 -->
            <div class="login-left">
                <div class="system-logo">
                    🏃‍♂️ 大学生体质测试管理系统
                </div>
                <div class="system-title">
                    欢迎使用 大学生体质测试管理系统
                </div>

                <ul class="feature-list">
                    <li>SpringBoot</li>
                    <li>MyBatis</li>
                    <li>Shiro</li>
                    <li>Thymeleaf</li>
                    <li>Bootstrap</li>
                </ul>

                <div class="copyright">
                    Copyright © 2018-2025 ruoyi.vip All Rights Reserved.
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="login-right">
                <div class="login-form-container">
                    <div class="login-title">登录</div>
                    <p style="text-align: center; color: #666; margin-bottom: 30px; font-size: 14px;">
                        欢迎使用大学生体质测试管理系统
                    </p>

                    <form id="signupForm" autocomplete="off">
                        <div class="form-group">
                            <input type="text" name="username" class="form-control" placeholder="admin" value="admin" />
                        </div>

                        <div class="form-group">
                            <input type="password" name="password" class="form-control" placeholder="········" value="admin123" />
                        </div>

                        <div class="form-group" th:if="${captchaEnabled==true}">
                            <div class="captcha-container">
                                <input type="text" name="validateCode" class="form-control captcha-input" placeholder="验证码" maxlength="5" />
                                <a href="javascript:void(0);" title="点击更换验证码">
                                    <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode captcha-image"/>
                                </a>
                            </div>
                        </div>

                        <div class="checkbox-custom" th:if="${isRemembered}">
                            <input type="checkbox" id="rememberme" name="rememberme">
                            <label for="rememberme">记住密码</label>
                        </div>

                        <button class="btn-login" id="btnSubmit" data-loading="正在验证登录，请稍候...">
                            登录
                        </button>

                        <div th:if="${isAllowRegister}" style="text-align: center; margin-bottom: 20px;">
                            <p style="color: #6c757d; font-size: 14px; margin: 0;">
                                还没有账号？
                                <a th:href="@{/register}" style="color: #667eea; text-decoration: none; font-weight: bold;">
                                    立即注册
                                </a>
                            </p>
                        </div>

                        <div class="login-footer">
                            <p style="color: #6c757d; font-size: 12px; margin: 0; line-height: 1.4;">
                                💡 提示：学号/工号作为用户名登录<br>
                                管理员账号：admin/admin123
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
</body>
</html>
