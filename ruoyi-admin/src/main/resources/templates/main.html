<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>大学生体质测试管理系统</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
    <div class="row border-bottom white-bg dashboard-header">
        <div class="col-sm-3">
            <h2>欢迎使用</h2>
            <small>大学生体质测试管理系统</small>
            <br>
            <br>
            <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white;">
                <i class="fa fa-heartbeat" style="font-size: 48px; margin-bottom: 10px;"></i>
                <h4 style="margin: 0; color: white;">体质健康</h4>
                <small style="color: rgba(255,255,255,0.9);">科学测试 数据驱动</small>
            </div>
            <br>
        </div>
        <div class="col-sm-5">
            <h2>大学生体质测试管理系统</h2>
            <p>本系统是专门为高等院校设计的<b>体质健康测试管理平台</b>，严格按照《国家学生体质健康标准》要求开发。系统集成了<b>学生信息管理</b>、<b>体测数据录入</b>、<b>成绩统计分析</b>、<b>报告生成</b>等核心功能，为学校体育院系/班级提供全方位的体质测试管理解决方案。通过科学的数据分析，帮助学校更好地了解学生体质健康状况，制定针对性的体育教学计划。</p>
            <p>
                <b>系统版本：</b><span>v[[${version}]]</span>
            </p>
            <p>
                <span class="label label-primary">🏃‍♂️ 体质健康</span>
                <span class="label label-success">📊 数据管理</span>
                <span class="label label-info">📈 统计分析</span>
            </p>
            <br>
            <p>
                <a class="btn btn-primary btn-outline" href="javascript:void(0);" onclick="showTestItems()">
                    <i class="fa fa-list-alt"> </i> 测试项目
                </a>
                <a class="btn btn-success btn-outline" href="javascript:void(0);" onclick="showSystemFeatures()">
                    <i class="fa fa-cogs"></i> 系统功能
                </a>
            </p>
        </div>
        <div class="col-sm-4">
            <h4>🎯 测试项目（国标）：</h4>
            <ol>
                <li><i class="fa fa-user"></i> 身高体重（BMI指数）</li>
                <li><i class="fa fa-heart"></i> 肺活量测试</li>
                <li><i class="fa fa-flash"></i> 50米跑（速度）</li>
                <li><i class="fa fa-arrow-up"></i> 立定跳远（爆发力）</li>
                <li><i class="fa fa-expand"></i> 坐位体前屈（柔韧性）</li>
                <li><i class="fa fa-male"></i> 仰卧起坐（女）/ 引体向上（男）</li>
                <li><i class="fa fa-clock-o"></i> 800米跑（女）/ 1000米跑（男）</li>
                <li><i class="fa fa-plus"></i> 其他选测项目</li>
            </ol>
        </div>

    </div>
    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-sm-4">

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>📞 系统信息</h5>

                    </div>
                    <div class="ibox-content">
                        <p><i class="fa fa-university"></i> 适用对象：高等院校体育院系/班级</p>
                        <p><i class="fa fa-users"></i> 服务对象：在校大学生</p>
                        <p><i class="fa fa-clipboard"></i> 测试标准：国家学生体质健康标准</p>
                        <p><i class="fa fa-shield"></i> 数据安全：云端存储，权限管控</p>
                        <p><i class="fa fa-phone"></i> 技术支持：体育信息化院系/班级</p>
                        <p><i class="fa fa-envelope"></i> 联系邮箱：<EMAIL></p>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>📈 系统统计</h5>
                    </div>
                    <div class="ibox-content">
                        <div style="padding: 15px;">
                            <div class="row text-center">
                                <div class="col-xs-6">
                                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                                        <h3 style="color: #667eea; margin: 0;">8</h3>
                                        <small>测试项目</small>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                                        <h3 style="color: #28a745; margin: 0;">100%</h3>
                                        <small>国标覆盖</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row text-center">
                                <div class="col-xs-6">
                                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                                        <h3 style="color: #ffc107; margin: 0;">5</h3>
                                        <small>等级评定</small>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                        <h3 style="color: #dc3545; margin: 0;">24/7</h3>
                                        <small>系统运行</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script type="text/javascript">
	    // 显示测试项目详情
	    function showTestItems() {
	        parent.layer.open({
	            title: '🎯 国家学生体质健康标准测试项目',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>必测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>身高体重：</strong>测量学生身高、体重，计算BMI指数</li>
	                        <li><strong>肺活量：</strong>测试学生呼吸系统功能</li>
	                        <li><strong>50米跑：</strong>测试学生速度素质和神经系统灵活性</li>
	                        <li><strong>坐位体前屈：</strong>测试学生柔韧性</li>
	                    </ul>
	                    <h4>选测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>立定跳远：</strong>测试学生下肢爆发力</li>
	                        <li><strong>仰卧起坐（女）：</strong>测试女学生腹肌耐力</li>
	                        <li><strong>引体向上（男）：</strong>测试男学生上肢力量</li>
	                        <li><strong>800米跑（女）/1000米跑（男）：</strong>测试学生心肺耐力</li>
	                    </ul>
	                    <p style="color: #666; margin-top: 15px;">
	                        <i class="fa fa-info-circle"></i>
	                        所有测试项目均按照教育部《国家学生体质健康标准》执行
	                    </p>
	                </div>
	            `
	        });
	    }

	    // 显示系统功能
	    function showSystemFeatures() {
	        parent.layer.open({
	            title: '🔧 系统核心功能',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>数据管理：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-users"></i> 学生信息批量导入与管理</li>
	                        <li><i class="fa fa-calendar"></i> 测试时间安排与预约管理</li>
	                        <li><i class="fa fa-edit"></i> 体测数据录入与修改</li>
	                        <li><i class="fa fa-shield"></i> 数据安全与权限控制</li>
	                    </ul>
	                </div>
	            `
	        });
	    }
    </script>
</body>
</html>


										</ol>
									</div>
								</div>
							</div>












    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script type="text/javascript">
	    // 显示测试项目详情
	    function showTestItems() {
	        parent.layer.open({
	            title: '🎯 国家学生体质健康标准测试项目',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>必测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>身高体重：</strong>测量学生身高、体重，计算BMI指数</li>
	                        <li><strong>肺活量：</strong>测试学生呼吸系统功能</li>
	                        <li><strong>50米跑：</strong>测试学生速度素质和神经系统灵活性</li>
	                        <li><strong>坐位体前屈：</strong>测试学生柔韧性</li>
	                    </ul>
	                    <h4>选测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>立定跳远：</strong>测试学生下肢爆发力</li>
	                        <li><strong>仰卧起坐（女）：</strong>测试女学生腹肌耐力</li>
	                        <li><strong>引体向上（男）：</strong>测试男学生上肢力量</li>
	                        <li><strong>800米跑（女）/1000米跑（男）：</strong>测试学生心肺耐力</li>
	                    </ul>
	                    <p style="color: #666; margin-top: 15px;">
	                        <i class="fa fa-info-circle"></i>
	                        所有测试项目均按照教育部《国家学生体质健康标准》执行
	                    </p>
	                </div>
	            `
	        });
	    }

	    // 显示系统功能
	    function showSystemFeatures() {
	        parent.layer.open({
	            title: '🔧 系统核心功能',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>数据管理：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-users"></i> 学生信息批量导入与管理</li>
	                        <li><i class="fa fa-calendar"></i> 测试时间安排与预约管理</li>
	                        <li><i class="fa fa-edit"></i> 体测数据录入与修改</li>
	                        <li><i class="fa fa-shield"></i> 数据安全与权限控制</li>
	                    </ul>
	                    <h4>统计分析：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-bar-chart"></i> 个人成绩统计与等级评定</li>
	                        <li><i class="fa fa-line-chart"></i> 班级、院系、全校数据对比</li>
	                        <li><i class="fa fa-pie-chart"></i> 体质健康趋势分析</li>
	                        <li><i class="fa fa-file-pdf-o"></i> 多维度报告自动生成</li>
	                    </ul>
	                    <h4>系统特色：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-mobile"></i> 支持PC端和移动端操作</li>
	                        <li><i class="fa fa-cloud"></i> 数据云端存储，安全可靠</li>
	                        <li><i class="fa fa-cogs"></i> 灵活的权限配置和角色管理</li>
	                    </ul>
	                </div>
	            `
	        });
	    }
    </script>
</body>
</html>
